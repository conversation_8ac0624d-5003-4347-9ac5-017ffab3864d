// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from '@/layout';

const hotelRouter = {
  path: '/hotel',
  component: Layout,
  redirect: '/hotel/room',
  name: 'Hotel',
  meta: {
    title: '酒店管理',
    icon: 'hotel',
  },
  children: [
    {
      path: 'room',
      component: () => import('@/views/hotel/room/index'),
      name: 'HotelRoom',
      meta: {
        title: '房型管理',
        icon: 'bed',
        perms: ['admin:hotel:room:list']
      },
    },
    {
      path: 'price',
      component: () => import('@/views/hotel/price/index'),
      name: 'HotelPrice',
      meta: {
        title: '价格策略',
        icon: 'money',
        perms: ['admin:hotel:price:list']
      },
    },
    {
      path: 'cancel',
      component: () => import('@/views/hotel/cancel/index'),
      name: 'HotelCancel',
      meta: {
        title: '取消规则',
        icon: 'document-remove',
        perms: ['admin:hotel:cancel:list']
      },
    },
  ],
};

export default hotelRouter;
