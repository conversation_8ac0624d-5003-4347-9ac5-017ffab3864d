<template>
  <view class="qr-code-container">
    <view class="qr-code-grid" :style="{ width: size + 'px', height: size + 'px' }">
      <!-- 生成21x21的二维码网格 -->
      <view v-for="(row, i) in qrPattern" :key="i" class="qr-row">
        <view
            v-for="(cell, j) in row"
            :key="j"
            class="qr-cell"
            :class="{ 'qr-black': cell }"
        ></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'QrCode',
  props: {
    // 二维码内容
    text: {
      type: String,
      required: true,
      default: ''
    },
    // 二维码大小
    size: {
      type: Number,
      default: 200
    },
    // 前景色
    colorDark: {
      type: String,
      default: '#000000'
    },
    // 背景色
    colorLight: {
      type: String,
      default: '#ffffff'
    },
    // canvas ID
    canvasId: {
      type: String,
      default: 'qrcode-canvas'
    }
  },
  data() {
    return {
      qrPattern: []
    }
  },
  watch: {
    text: {
      handler(newVal) {
        if (newVal) {
          this.generateQRPattern()
        }
      }
    }
  },
  mounted() {
    this.generateQRPattern()
  },
  methods: {
    // 生成二维码图案
    generateQRPattern() {
      const size = 21 // 21x21标准二维码
      const pattern = []

      // 初始化空白图案
      for (let i = 0; i < size; i++) {
        pattern[i] = new Array(size).fill(false)
      }

      // 绘制定位标记
      this.drawFinderPattern(pattern, 0, 0) // 左上
      this.drawFinderPattern(pattern, 0, 14) // 右上
      this.drawFinderPattern(pattern, 14, 0) // 左下

      // 绘制分隔符
      this.drawSeparators(pattern)

      // 绘制时序图案
      this.drawTimingPatterns(pattern)

      // 填充数据区域
      if (this.text) {
        this.fillDataModules(pattern)
      }

      this.qrPattern = pattern

      this.$emit('success', {
        text: this.text,
        size: this.size
      })
    },

    // 绘制定位标记
    drawFinderPattern(pattern, row, col) {
      for (let i = 0; i < 7; i++) {
        for (let j = 0; j < 7; j++) {
          if (row + i < 21 && col + j < 21) {
            // 外框
            if (i === 0 || i === 6 || j === 0 || j === 6) {
              pattern[row + i][col + j] = true
            }
            // 内部空白
            else if (i === 1 || i === 5 || j === 1 || j === 5) {
              pattern[row + i][col + j] = false
            }
            // 中心块
            else if (i >= 2 && i <= 4 && j >= 2 && j <= 4) {
              pattern[row + i][col + j] = true
            }
          }
        }
      }
    },

    // 绘制分隔符
    drawSeparators(pattern) {
      // 左上角分隔符
      for (let i = 0; i < 8; i++) {
        if (i < 21) pattern[7][i] = false
        if (i < 21) pattern[i][7] = false
      }
      // 右上角分隔符
      for (let i = 0; i < 8; i++) {
        if (14 + i < 21) pattern[7][14 + i] = false
        if (i < 21) pattern[i][13] = false
      }
      // 左下角分隔符
      for (let i = 0; i < 8; i++) {
        if (14 + i < 21) pattern[14 + i][7] = false
        if (i < 21) pattern[13][i] = false
      }
    },

    // 绘制时序图案
    drawTimingPatterns(pattern) {
      for (let i = 8; i < 13; i++) {
        pattern[6][i] = (i % 2 === 0)
        pattern[i][6] = (i % 2 === 0)
      }
    },

    // 填充数据模块
    fillDataModules(pattern) {
      const hash = this.hashCode(this.text)

      // 创建更真实的二维码图案
      for (let i = 0; i < 21; i++) {
        for (let j = 0; j < 21; j++) {
          if (!this.isReservedModule(i, j)) {
            // 使用多种算法混合生成更真实的图案
            const value1 = (hash + i * 13 + j * 17) % 100
            const value2 = (hash * 7 + i * j * 3) % 100
            const value3 = (i + j + hash) % 100

            // 组合多个值，创建更复杂的图案
            const combined = (value1 + value2 + value3) % 100

            // 在某些区域增加密度
            let threshold = 45
            if ((i + j) % 3 === 0) threshold = 35
            if ((i * j) % 7 === 0) threshold = 55

            pattern[i][j] = combined < threshold
          }
        }
      }

      // 添加一些随机的连接线，使其更像真实二维码
      this.addConnectingLines(pattern, hash)
    },

    // 添加连接线
    addConnectingLines(pattern, hash) {
      for (let i = 8; i < 13; i++) {
        for (let j = 8; j < 13; j++) {
          if (!this.isReservedModule(i, j)) {
            // 在中心区域添加一些连接图案
            if ((i + j + hash) % 4 === 0) {
              pattern[i][j] = true
            }
          }
        }
      }
    },

    // 检查是否是保留模块
    isReservedModule(row, col) {
      // 定位标记区域
      if ((row < 9 && col < 9) ||
          (row < 9 && col > 11) ||
          (row > 11 && col < 9)) {
        return true
      }
      // 时序图案
      if ((row === 6 && col >= 8 && col <= 12) ||
          (col === 6 && row >= 8 && row <= 12)) {
        return true
      }
      return false
    },

    // 简单的字符串哈希函数
    hashCode(str) {
      let hash = 0
      if (!str || str.length === 0) return hash
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash
      }
      return Math.abs(hash)
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-grid {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.qr-row {
  display: flex;
  height: calc(100% / 21);
}

.qr-cell {
  width: calc(100% / 21);
  height: 100%;
  background-color: #fff;

  &.qr-black {
    background-color: #333;
    border-radius: 1rpx;
  }
}
</style>
