{
	"easycom": {
		//easycom,按需自动注册组件。原则上可以把所有页面引入组件方法删掉，会自动引入。
		"autoscan": true,
		"custom": {
			"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue",
			// "^uni-(.*)": "@/components/uni-$1/uni-$1.vue",
			"^nx-(.*)": "@/components/nx-$1/nx-$1.vue"
		}
	},
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		// 景区首页
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		// 我的
		{
			"path": "pages/me/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		// 个人资料
		{
			"path": "pages/me/userInfo",
			"style": {
				"navigationBarTitleText": "个人资料"
			}
		},
		// 龙宫币记录
		{
			"path": "pages/me/coinRecord",
			"style": {
				"navigationBarTitleText": "龙宫币记录"
            },
          "meta": {
            "auth": true
          }
        },
      // 订单详情
      {
        "path": "pages/me/order/detail",
        "style": {
          "navigationBarTitleText": "订单详情"
			},
			"meta": {
				"auth": true
			}
		},
		// 云游金梭
		{
			"path": "pages/vr/index",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		// 玩转金梭
		{
			"path": "pages/play/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		// 积分商城
		{
			"path": "pages/shopping/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		// 玩转金梭--攻略详情
		{
			"path": "pages/play/playDetail",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor":"#E9F4F7"
			}
		},
		// 投诉建议
		{
			"path": "pages/complaints/complaints",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			},
			"meta": {
				"auth": true
			}
		},
		// 投诉建议-管理员
		{
			"path" : "pages/complaints/complaintList",
			"style" :
			{
				"navigationBarTitleText" : "投诉建议"
			},
			"meta": {
				"auth": true
			}
		},
		// 金梭小管家
		{
			"path": "pages/me/chamberlain",
			"style": {
				"navigationBarTitleText": "金梭小管家",
				"navigationBarBackgroundColor":"#E9F4F7"
			}
		},
		// 问卷调查
		{
			"path" : "pages/me/questionnaire",
			"style" :
			{
				"navigationBarTitleText" : ""
			},
			"meta": {
				"auth": true
			}
		},
		// 游览路线vr
		{
			"path" : "pages/vr/touristRoute",
			"style" :
			{
				"navigationBarTitleText" : "大理金梭岛旅游攻略"
			}
		},
		// 酒店民宿vr
		{
			"path" : "pages/vr/hotelHomestay",
			"style" :
			{
				"navigationBarTitleText" : "大理金梭岛旅游攻略"
			}
        },
      // 支付页面
      {
        "path": "pages/payment/index",
        "style": {
          "navigationBarTitleText": "订单支付"
        }
		}
	],
	"subPackages": [
		{
			"root": "pages/public",
			"pages": [
				{
					"path": "404",
					"style": {
						"navigationBarTitleText": "页面不存在"
					}
				},
				// 图片页面
				{
					"path": "imagPage",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			// 金梭资讯
			"root": "pages/news",
			"pages": [
				// 金梭资讯列表
				{
					"path": "list",
					"style": {
						"navigationBarTitleText": "金梭资讯"
					}
				},
				// 资讯，攻略详情
				{
					"path": "infoDetail",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarBackgroundColor":"#E9F4F7"
					}
				},
				// 景区公告
				{
					"path": "notice",
					"style": {
						"navigationBarTitleText": "景区公告"
					}
				}
			]
		},
		{
			// 商户
			"root": "pages/merchant",
			"pages": [
				// 商户详情
				{
					"path": "merchantDetail",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "videoPage",
					"style" :
					{
						"navigationBarTitleText" : "",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "imgPage",
					"style" :
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "map",
					"style" :
					{
						"navigationBarTitleText" : "地图导航"
					}
				}
			]
		},
		{
			// 应急救援
			"root": "pages/emergency",
			"pages": [
				{
					"path": "emergency",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				// 应急救援-详情
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		},
		{
			"root": "pages/login",
			"pages": [
				{
					// 登录
					"path": "login",
					"aliasPath": "/", //对于h5端你必须在首页加上aliasPath并设置为/
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					}
				},
				// 注册
				{
					"path": "register",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					}
				},
				// 修改密码
				{
					"path": "password",
					"style": {
						"navigationBarTitleText": "修改密码"
					}
				},
				// 用户服务协议
				{
					"path": "agreement",
					"style": {
						"navigationBarTitleText": "用户服务协议"
					}
				}
			]
		},
		{
			"root": "pages/shopping/collect",
			"pages": [
				{
					// 收藏
					"path": "index",
					"style": {
						"navigationBarTitleText": "我的收藏",
						"navigationBarBackgroundColor": "#F5F5F5" // 设置导航栏背景颜色
					}
				}
			]
		},
		{
			"root": "pages/shopping/coupon",
			"pages": [
				{
					// 我的优惠券
					"path": "index",
					"style": {
						"navigationBarTitleText": "我的优惠券",
						"navigationBarBackgroundColor": "#F5F5F5" // 设置导航栏背景颜色
					}
				}
			]
		},
		{
			"root": "pages/shopping/qualityMerchant",
			"pages": [
				{
					// 品质商家列表
					"path": "index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					// 品质商家详情
					"path": "details",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "pages/shopping/points",
			"pages": [
				{
					// 积分购物
					"path": "index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					}
				},
				{
					// 我的积分
					"path": "info",
					"style": {
						"navigationBarTitleText": "我的积分",
						"navigationBarBackgroundColor":"#f5f5f5"
					}
				}
			]
		},
		{
			"root": "pages/shopping/hotel",
			"pages": [
				{
					// 酒店列表
					"path": "index",
					"style": {
						"navigationBarTitleText": "酒店预订"
					}
				},
				{
					// 酒店详情
					"path": "detail",
					"style": {
						"navigationBarTitleText": "酒店详情"
					}
				},
				{
					// 房型列表
					"path": "rooms",
					"style": {
						"navigationBarTitleText": "选择房型"
					}
				},
				{
					// 日期选择
					"path": "date-select",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "选择日期"
					}
				},
				{
					// 预订确认
					"path": "booking",
					"style": {
						"navigationBarTitleText": "确认预订"
					}
				}
			]
		},
		{
			"root": "pages/address",
			"pages": [
				{
					// 登录
					"path": "index",
					"style": {
						"navigationBarTitleText": "收货地址"
					}
				},
				// 注册
				{
					"path": "edit",
					"style": {
						"navigationBarTitleText": "编辑地址"
					}
				},
				// 地图选择
				{
					"path": "map",
					"style": {
						"navigationBarTitleText": "地图地址"
					}
				}
			]
		},
		{
			"root": "pages/distribution",
			"pages": [
				{
					"path": "index",
					"style": {
						"navigationBarTitleText": "分销赚钱"
					}
				}
			]
		}
	],

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "通用模板",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"borderStyle": "white",
		"selectedColor": "#2DA6C4",
		"backgroundColor": "#FFFFFF",
		"color": "#999999",
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "static/images/tabbar/tab-home.png",
				"selectedIconPath": "static/images/tabbar/home-selected.png",
				"text": "景区首页"
			},
			{
				"pagePath": "pages/play/index",
				"iconPath": "static/images/tabbar/tab-play.png",
				"selectedIconPath": "static/images/tabbar/play-selected.png",
				"text": "玩转金梭"
			},
			{
				"pagePath": "pages/vr/index",
				"iconPath": "static/images/tabbar/tab-vr.png",
				"selectedIconPath": "static/images/tabbar/vr-selected.png",
				"text": "云游金梭"
			},
			{
				"pagePath": "pages/shopping/index",
				"iconPath": "static/images/tabbar/tab-points.png",
				"selectedIconPath": "static/images/tabbar/points-selected.png",
				"text": "积分商城"
			},
			{
				"pagePath": "pages/me/index",
				"iconPath": "static/images/tabbar/tab-me.png",
				"selectedIconPath": "static/images/tabbar/me-selected.png",
				"text": "我的"
			}
		]
	},
	"condition": {
		//模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "登录页面", //模式名称
				"path": "pages/login/login", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
