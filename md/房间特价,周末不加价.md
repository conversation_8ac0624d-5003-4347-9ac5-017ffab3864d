# 特价房源 & 周末不加价功能详细执行计划

## 🎯 阶段一：数据库和后端核心逻辑

### 1.1 数据库结构扩展

**文件：** `sql/hotel_special_offer_upgrade.sql`

```sql
-- 房型表新增特价相关字段
ALTER TABLE eb_hotel_room 
ADD COLUMN is_special_offer TINYINT(1) DEFAULT 0 COMMENT '是否特价房源',
ADD COLUMN weekend_no_markup TINYINT(1) DEFAULT 0 COMMENT '周末不加价';

-- 取消规则表支持特价房源专用规则
ALTER TABLE eb_hotel_cancel_rule 
ADD COLUMN is_for_special_offer TINYINT(1) DEFAULT 0 COMMENT '是否适用于特价房源';

-- 价格策略表新增特价策略类型（可选）
-- strategy_type: 6=特价策略
```

### 1.2 实体类扩展

**修改文件：**

- `crmeb-service/src/main/java/com/zbkj/service/model/hotel/HotelRoom.java`
- `crmeb-service/src/main/java/com/zbkj/service/model/hotel/HotelCancelRule.java`

**新增字段：**

```java
// HotelRoom.java
private Integer isSpecialOffer;  // 是否特价房源
private Integer weekendNoMarkup; // 周末不加价

// HotelCancelRule.java  
private Integer isForSpecialOffer; // 是否适用于特价房源
```

### 1.3 价格计算引擎升级

**修改文件：** `crmeb-service/src/main/java/com/zbkj/service/service/hotel/impl/HotelBookingServiceImpl.java`

**核心方法：** `calculatePrice()`

```java
// 新增特价房源价格计算逻辑
private BigDecimal calculatePriceWithSpecialOffer(Integer roomId, Date date) {
    // 1. 获取房型信息
    HotelRoom room = hotelRoomService.getById(roomId);
    
    // 2. 特价房源优先级最高
    if (room.getIsSpecialOffer() == 1) {
        BigDecimal specialPrice = findSpecialOfferPrice(roomId, date);
        if (specialPrice != null) return specialPrice;
    }
    
    // 3. 周末不加价处理
    if (room.getWeekendNoMarkup() == 1 && isWeekend(date)) {
        return findWorkdayPrice(roomId, date);
    }
    
    // 4. 原有价格计算逻辑
    return calculatePriceByDateType(roomId, date);
}
```

### 1.4 定时任务商品生成扩展

**修改文件：** `crmeb-service/src/main/java/com/zbkj/service/service/hotel/impl/HotelProductSyncServiceImpl.java`

**修改方法：** `createProduct()`

```java
// 商品生成时标记特价属性
private Product createProduct(HotelRoom room, Date date, BigDecimal price) {
    Product product = new Product();
    
    // 原有逻辑...
    
    // 特价房源标记
    if (room.getIsSpecialOffer() == 1) {
        product.setProductName(product.getProductName() + "[特价]");
        // 可利用现有字段标记特价，如keyword字段
        product.setKeyword("special_offer");
    }
    
    // 周末不加价标记
    if (room.getWeekendNoMarkup() == 1) {
        product.setKeyword(product.getKeyword() + ",weekend_no_markup");
    }
    
    return product;
}
```

## 🎯 阶段二：管理端功能开发

### 2.1 后端接口扩展

**修改文件：** `crmeb-admin/src/main/java/com/zbkj/admin/controller/hotel/HotelRoomController.java`

**新增接口：**

```java
@ApiOperation("设置特价房源")
@PostMapping("/setSpecialOffer")
public CommonResult<String> setSpecialOffer(@RequestBody HotelRoomSpecialOfferRequest request) {
    // 设置房型的特价和周末不加价属性
}

@ApiOperation("获取特价房源列表")  
@GetMapping("/specialOfferList")
public CommonResult<List<HotelRoomResponse>> getSpecialOfferList() {
    // 查询特价房源列表
}
```

### 2.2 请求响应类

**新增文件：** `crmeb-admin/src/main/java/com/zbkj/admin/request/hotel/HotelRoomSpecialOfferRequest.java`

```java
public class HotelRoomSpecialOfferRequest {
    private Integer roomId;
    private Integer isSpecialOffer;    // 是否特价房源
    private Integer weekendNoMarkup;   // 周末不加价
}
```

### 2.3 Service层扩展

**修改文件：** `crmeb-service/src/main/java/com/zbkj/service/service/hotel/impl/HotelRoomServiceImpl.java`

**新增方法：**

```java
// 设置特价房源
public Boolean setSpecialOffer(HotelRoomSpecialOfferRequest request);

// 获取特价房源列表
public List<HotelRoom> getSpecialOfferList(Integer merId);

// 批量设置周末不加价
public Boolean batchSetWeekendNoMarkup(List<Integer> roomIds, Integer status);
```

### 2.4 前端页面改造

**修改文件：** `dljs-sys-mer-web/src/views/hotel/room/index.vue`

**新增功能：**

- 房型列表显示特价和周末不加价标识
- 批量操作：设置特价房源、开启周末不加价
- 筛选功能：按特价房源、周末不加价筛选

**修改文件：** `dljs-sys-mer-web/src/views/hotel/room/components/RoomForm.vue`

**新增表单项：**

```vue
<!-- 特价房源开关 -->
<el-form-item label="特价房源">
  <el-switch v-model="formData.isSpecialOffer" :active-value="1" :inactive-value="0"/>
  <span class="form-tip">开启后此房型将显示特价标签</span>
</el-form-item>

<!-- 周末不加价开关 -->
<el-form-item label="周末不加价">  
  <el-switch v-model="formData.weekendNoMarkup" :active-value="1" :inactive-value="0"/>
  <span class="form-tip">开启后周末价格与工作日相同</span>
</el-form-item>
```

## 🎯 阶段三：用户端展示功能

### 3.1 后端接口扩展

**修改文件：** `crmeb-front/src/main/java/com/zbkj/front/controller/hotel/HotelBookingController.java`

**修改响应类：** `HotelRoomListResponse`

```java
public class HotelRoomListResponse {
    // 原有字段...
    
    private Integer isSpecialOffer;    // 是否特价房源
    private Integer weekendNoMarkup;   // 周末不加价
    private String specialOfferDesc;   // 特价说明
}
```

### 3.2 移动端页面改造

**修改文件：** `dljs-app/pages/shopping/hotel/rooms.vue`

**新增展示元素：**

```vue
<!-- 特价房源标签 -->
<view v-if="room.isSpecialOffer" class="special-offer-tag">
  <text>特价房源</text>
  <text class="sub-text">随时退 过期退</text>
</view>

<!-- 周末不加价标签 -->
<view v-if="room.weekendNoMarkup" class="weekend-no-markup-tag">
  <text>周末不加价</text>
  <text class="sub-text">平价玩到嗨</text>
</view>
```

**修改文件：** `dljs-app/pages/shopping/hotel/detail.vue`

**新增特价说明区域：**

```vue
<!-- 特价房源优势说明 -->
<view v-if="hotelDetail.hasSpecialOffer" class="special-offer-section">
  <view class="section-title">特价房源优势</view>
  <view class="advantage-list">
    <view class="advantage-item">✓ 随时可退，无手续费</view>
    <view class="advantage-item">✓ 过期自动退款</view>
    <view class="advantage-item">✓ 价格优惠，性价比高</view>
  </view>
</view>
```

### 3.3 样式设计

**新增样式文件：** `dljs-app/static/css/hotel-special.css`

```css
/* 特价房源标签样式 */
.special-offer-tag {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* 周末不加价标签样式 */
.weekend-no-markup-tag {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}
```

## 📋 开发检查清单

### 数据库层

- [ ] 执行DDL脚本，新增字段
- [ ] 验证字段类型和默认值
- [ ] 更新实体类映射

### 后端开发

- [ ] 修改价格计算引擎
- [ ] 扩展房型管理Service
- [ ] 新增特价设置接口
- [ ] 修改定时任务逻辑
- [ ] 扩展取消规则计算

### 前端开发

- [ ] 商户端房型管理页面改造
- [ ] 新增特价设置表单
- [ ] 用户端房型列表标签显示
- [ ] 详情页面特价说明
- [ ] 样式和交互优化

### 测试验证

- [ ] 特价房源价格计算正确性
- [ ] 周末不加价逻辑验证
- [ ] 商户端设置功能测试
- [ ] 用户端展示效果测试
- [ ] 定时任务商品生成测试

这个详细计划涵盖了从数据库到前端的完整实施路径，每个阶段都有明确的文件修改清单和代码示例。





# 定时任务中特价房源和周末不加价的实现方案

## 🔍 当前定时任务分析

从代码中看到，当前定时任务的核心流程是：

1. **HotelProductSyncServiceImpl.createNewProduct()** - 创建新商品
2. **calculatePrice(roomId, checkInDate)** - 调用价格计算引擎
3. **商品属性设置** - 设置商品名称、价格、库存等

## 🎯 特价房源和周末不加价的定时任务改造方案

### 1. 价格计算引擎升级（核心修改）

**修改文件：** `HotelProductSyncServiceImpl.calculatePrice()` 方法

```java
private BigDecimal calculatePrice(Integer roomId, LocalDate date) {
    try {
        // 🏨 步骤1：获取房型信息（新增）
        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房间 {} 不存在", roomId);
            return BigDecimal.ZERO;
        }
        
        // 🗓️ 步骤2：调用中国日历服务，获取日期类型
        DateTypeEnum dateType = chineseCalendarService.getDateType(date);
        log.debug("房间 {} 日期 {} 识别为：{}", roomId, date, dateType.getDesc());

        // 📋 步骤3：查询房间的所有启用价格策略
        List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
        
        if (CollUtil.isEmpty(strategies)) {
            log.warn("房间 {} 没有配置价格策略，返回0元", roomId);
            return BigDecimal.ZERO;
        }

        // 🎯 步骤4：特价房源优先级处理（新增逻辑）
        if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
            // 查找特价策略（strategy_type = 6）
            BigDecimal specialPrice = findSpecialOfferPrice(strategies, dateType, date);
            if (specialPrice != null && specialPrice.compareTo(BigDecimal.ZERO) > 0) {
                log.info("房间 {} 日期 {} 使用特价策略，价格：{}", roomId, date, specialPrice);
                return specialPrice;
            }
        }
        
        // 🎯 步骤5：周末不加价处理（新增逻辑）
        if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
            if (dateType == DateTypeEnum.WEEKEND) {
                // 周末不加价，强制使用工作日价格
                BigDecimal workdayPrice = findWorkdayPrice(strategies);
                if (workdayPrice != null && workdayPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("房间 {} 日期 {} 启用周末不加价，使用工作日价格：{}", roomId, date, workdayPrice);
                    return workdayPrice;
                }
            }
        }

        // 🎯 步骤6：原有策略匹配逻辑
        return strategies.stream()
            .filter(s -> matchesDateType(s, dateType, date))
            .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
            .map(HotelRoomPriceStrategy::getPriceValue)
            .orElse(BigDecimal.ZERO);

    } catch (Exception e) {
        log.error("计算价格失败，房间ID: {}, 日期: {}", roomId, date, e);
        return BigDecimal.ZERO;
    }
}

// 新增辅助方法：查找特价策略价格
private BigDecimal findSpecialOfferPrice(List<HotelRoomPriceStrategy> strategies, DateTypeEnum dateType, LocalDate date) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 6) // 特价策略类型
        .filter(s -> matchesDateType(s, dateType, date))
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}

// 新增辅助方法：查找工作日价格
private BigDecimal findWorkdayPrice(List<HotelRoomPriceStrategy> strategies) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 1) // 工作日策略类型
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}
```

### 2. 商品生成时的标记处理

**修改文件：** `HotelProductSyncServiceImpl.createNewProduct()` 方法

```java
private Product createNewProduct(HotelRoom room, Merchant merchant, LocalDate checkInDate) {
    Product product = new Product();
    
    // 原有商品名称生成
    String baseName = generateProductName(merchant.getName(), room.getRoomName(), checkInDate);
    
    // 🏷️ 特价房源标记处理
    StringBuilder productName = new StringBuilder(baseName);
    StringBuilder keywords = new StringBuilder();
    
    if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
        productName.append("[特价]");
        keywords.append("special_offer");
        log.info("商品 {} 标记为特价房源", productName);
    }
    
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        productName.append("[周末不加价]");
        if (keywords.length() > 0) keywords.append(",");
        keywords.append("weekend_no_markup");
        log.info("商品 {} 标记为周末不加价", productName);
    }
    
    product.setName(productName.toString());
    product.setKeyword(keywords.toString()); // 利用现有字段存储标记
    
    // 原有逻辑继续...
    // 价格计算（会自动调用升级后的calculatePrice方法）
    BigDecimal price = calculatePrice(room.getId(), checkInDate);
    product.setPrice(price);
    
    // 其他设置保持不变...
    return product;
}
```

### 3. 商品更新时的处理

**修改文件：** `HotelProductSyncServiceImpl.updateExistingProduct()` 方法

```java
private void updateExistingProduct(Product product, HotelRoom room, LocalDate checkInDate) {
    // 🔄 重新生成商品名称（包含最新标记）
    String merchantName = extractMerchantNameFromProduct(product);
    String baseName = generateProductName(merchantName, room.getRoomName(), checkInDate);
    
    StringBuilder productName = new StringBuilder(baseName);
    StringBuilder keywords = new StringBuilder();
    
    // 特价房源标记
    if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
        productName.append("[特价]");
        keywords.append("special_offer");
    }
    
    // 周末不加价标记
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        productName.append("[周末不加价]");
        if (keywords.length() > 0) keywords.append(",");
        keywords.append("weekend_no_markup");
    }
    
    product.setName(productName.toString());
    product.setKeyword(keywords.toString());
    
    // 🔄 重新计算价格（会自动应用新的价格逻辑）
    BigDecimal newPrice = calculatePrice(room.getId(), checkInDate);
    product.setPrice(newPrice);
    product.setOtPrice(newPrice.multiply(new BigDecimal("1.2")));
    product.setCost(newPrice.multiply(new BigDecimal("0.8")));
    
    // 其他更新逻辑保持不变...
    productService.updateById(product);
    updateProductAttrValue(product.getId(), newPrice, room.getTotalRooms());
}
```

## 🎯 定时任务执行效果

### 特价房源商品生成示例：
- **原商品名：** `大粽子的杂货店-精品大床房-2025-01-15`
- **特价商品名：** `大粽子的杂货店-精品大床房-2025-01-15[特价]`
- **价格计算：** 优先使用特价策略（strategy_type=6）
- **商品标记：** keyword字段包含"special_offer"

### 周末不加价商品生成示例：
- **原商品名：** `大粽子的杂货店-豪华大床房-2025-01-18`（周六）
- **周末不加价商品名：** `大粽子的杂货店-豪华大床房-2025-01-18[周末不加价]`
- **价格计算：** 周六使用工作日价格策略（strategy_type=1）
- **商品标记：** keyword字段包含"weekend_no_markup"

### 组合标记示例：
- **商品名：** `大粽子的杂货店-情侣房-2025-01-19[特价][周末不加价]`
- **价格优先级：** 特价策略 > 周末不加价 > 普通策略
- **商品标记：** keyword字段包含"special_offer,weekend_no_markup"

## ✅ 实施优势

1. **零侵入性** - 完全基于现有定时任务架构，不破坏原有逻辑
2. **自动化处理** - 定时任务自动识别房型设置，生成对应标记的商品
3. **价格准确性** - 价格计算引擎自动应用特价和周末不加价逻辑
4. **标记清晰** - 商品名称和keyword字段双重标记，便于前端识别
5. **向后兼容** - 现有房型和商品完全不受影响

这样设计后，每天凌晨2点的定时任务会自动：
- 检查每个房型的特价和周末不加价设置
- 根据设置应用相应的价格计算逻辑
- 生成带有正确标记和价格的商品
- 确保前端能够正确识别和展示特价信息

# 🕐 定时任务处理方案 - 特价房源 & 周末不加价

## 📋 当前定时任务执行流程分析

```
HotelProductSyncTask.syncHotelProducts() (每日凌晨2点)
↓
HotelProductSyncServiceImpl.syncAllHotelProducts()
↓
按商户分组 → syncSingleRoom()
↓
生成未来30天商品 → generateProductName() + calculatePrice()
↓
createNewProduct() / updateExistingProduct()
```

### 核心执行方法分析
1. **HotelProductSyncServiceImpl.createNewProduct()** - 创建新商品
2. **calculatePrice(roomId, checkInDate)** - 调用价格计算引擎
3. **generateProductName()** - 生成商品名称
4. **buildHotelProduct()** - 构建商品对象
5. **商品属性设置** - 设置商品名称、价格、库存等

## 🎯 定时任务修改方案

### 1. 价格计算引擎升级（核心修改）

**修改文件：** `HotelProductSyncServiceImpl.calculatePrice()` 方法

**当前逻辑**: 只处理策略类型1-3（基础、周末、节假日）
**修改后逻辑**:
1. 优先检查特价策略（策略类型6，优先级200）
2. 处理周末不加价逻辑（房型属性）
3. 执行原有价格计算（策略类型1-3）

```java
private BigDecimal calculatePrice(Integer roomId, LocalDate date) {
    try {
        // 步骤1：获取房型信息（新增）
        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房间 {} 不存在", roomId);
            return BigDecimal.ZERO;
        }
        
        // 步骤2：获取日期类型
        DateTypeEnum dateType = chineseCalendarService.getDateType(date);
        
        // 步骤3：查询价格策略
        List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
        
        // 步骤4：特价策略优先处理（新增）
        BigDecimal specialPrice = findSpecialOfferPrice(strategies);
        if (specialPrice != null && specialPrice.compareTo(BigDecimal.ZERO) > 0) {
            log.info("房间 {} 日期 {} 使用特价策略，价格：{}", roomId, date, specialPrice);
            return specialPrice;
        }
        
        // 步骤5：周末不加价处理（新增）
        if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
            if (dateType == DateTypeEnum.WEEKEND) {
                BigDecimal workdayPrice = findWorkdayPrice(strategies);
                if (workdayPrice != null && workdayPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("房间 {} 日期 {} 启用周末不加价，使用工作日价格：{}", roomId, date, workdayPrice);
                    return workdayPrice;
                }
            }
        }
        
        // 步骤6：原有价格计算逻辑（保持不变）
        return strategies.stream()
            .filter(s -> matchesDateType(s, dateType, date))
            .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
            .map(HotelRoomPriceStrategy::getPriceValue)
            .orElse(BigDecimal.ZERO);
            
    } catch (Exception e) {
        log.error("计算价格失败，房间ID: {}, 日期: {}", roomId, date, e);
        return BigDecimal.ZERO;
    }
}
```

### 2. 商品名称生成升级

**修改文件：** `HotelProductSyncServiceImpl.generateProductName()` 方法

**当前格式**: `{商户名}-{房型名}-{日期}`
**修改后格式**: `{商户名}-{房型名}-{日期}[特价][周末不加价]`

```java
private String generateProductName(String hotelName, String roomName, LocalDate checkInDate, HotelRoom room) {
    StringBuilder name = new StringBuilder();
    name.append(String.format("%s-%s-%s", hotelName, roomName, 
                checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
    
    // 检查特价策略（策略类型6）
    if (hasActiveSpecialPrice(room.getId(), checkInDate)) {
        name.append("[特价]");
    }
    
    // 检查周末不加价
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        DateTypeEnum dateType = chineseCalendarService.getDateType(checkInDate);
        if (dateType == DateTypeEnum.WEEKEND) {
            name.append("[周末不加价]");
        }
    }
    
    return name.toString();
}
```

### 3. 商品属性设置升级

**修改文件：** `HotelProductSyncServiceImpl.buildHotelProduct()` 方法

**当前关键词**: `"酒店,住宿,预订," + room.getRoomName()`
**修改后关键词**: 动态添加 `special_offer` 和 `weekend_no_markup` 标记

```java
private void setProductKeywords(Product product, HotelRoom room, LocalDate date) {
    List<String> keywords = new ArrayList<>();
    keywords.add("酒店");
    keywords.add("住宿");
    keywords.add("预订");
    keywords.add(room.getRoomName());
    
    // 特价标记
    if (hasActiveSpecialPrice(room.getId(), date)) {
        keywords.add("special_offer");
    }
    
    // 周末不加价标记
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        keywords.add("weekend_no_markup");
    }
    
    product.setKeyword(String.join(",", keywords));
}
```

### 4. 新增辅助方法

**在HotelProductSyncServiceImpl中新增以下方法：**

```java
/**
 * 检查是否有生效的特价策略
 */
private boolean hasActiveSpecialPrice(Integer roomId, LocalDate date) {
    List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
    return strategies.stream()
        .anyMatch(s -> s.getStrategyType() == 6 && s.getStatus() == 1);
}

/**
 * 查找特价策略价格
 */
private BigDecimal findSpecialOfferPrice(List<HotelRoomPriceStrategy> strategies) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 6 && s.getStatus() == 1)
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}

/**
 * 查找工作日价格
 */
private BigDecimal findWorkdayPrice(List<HotelRoomPriceStrategy> strategies) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 1 && s.getStatus() == 1)
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}
```

## 🎯 定时任务执行效果示例

### 普通房源商品生成：
- **商品名：** `大粽子的杂货店-精品大床房-2025-01-15`
- **价格计算：** 按原有策略计算（工作日/周末/节假日）
- **关键词：** `酒店,住宿,预订,精品大床房`

### 特价房源商品生成：
- **商品名：** `大粽子的杂货店-精品大床房-2025-01-15[特价]`
- **价格计算：** 优先使用特价策略（strategy_type=6，优先级200）
- **关键词：** `酒店,住宿,预订,精品大床房,special_offer`

### 周末不加价商品生成：
- **商品名：** `大粽子的杂货店-豪华大床房-2025-01-18[周末不加价]`（周六）
- **价格计算：** 周六使用工作日价格策略（strategy_type=1）
- **关键词：** `酒店,住宿,预订,豪华大床房,weekend_no_markup`

### 组合标记：
- **商品名：** `大粽子的杂货店-情侣房-2025-01-19[特价][周末不加价]`
- **价格优先级：** 特价策略(200) > 周末不加价 > 普通策略
- **关键词：** `酒店,住宿,预订,情侣房,special_offer,weekend_no_markup`

## ✅ 实施优势

1. **零侵入性** - 完全基于现有定时任务架构，不破坏原有逻辑
2. **自动化处理** - 定时任务自动识别房型设置，生成对应标记的商品
3. **价格准确性** - 价格计算引擎自动应用特价和周末不加价逻辑
4. **标记清晰** - 商品名称和keyword字段双重标记，便于前端识别
5. **向后兼容** - 现有房型和商品完全不受影响

定时任务详细方案已完成，包含了所有核心修改点和执行效果示例。
