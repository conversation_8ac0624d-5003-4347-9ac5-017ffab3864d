# 🕐 定时任务处理方案 - 特价房源 & 周末不加价

## 📋 当前定时任务执行流程分析

```
HotelProductSyncTask.syncHotelProducts() (每日凌晨2点)
↓
HotelProductSyncServiceImpl.syncAllHotelProducts()
↓
按商户分组 → syncSingleRoom()
↓
生成未来30天商品 → generateProductName() + calculatePrice()
↓
createNewProduct() / updateExistingProduct()
```

### 核心执行方法分析
1. **HotelProductSyncServiceImpl.createNewProduct()** - 创建新商品
2. **calculatePrice(roomId, checkInDate)** - 调用价格计算引擎
3. **generateProductName()** - 生成商品名称
4. **buildHotelProduct()** - 构建商品对象
5. **商品属性设置** - 设置商品名称、价格、库存等

## 🎯 定时任务修改方案

### 1. 价格计算引擎升级（核心修改）

**修改文件：** `HotelProductSyncServiceImpl.calculatePrice()` 方法

**当前逻辑**: 只处理策略类型1-3（基础、周末、节假日）
**修改后逻辑**: 
1. 优先检查特价策略（策略类型6，优先级200）
2. 处理周末不加价逻辑（房型属性）
3. 执行原有价格计算（策略类型1-3）

```java
private BigDecimal calculatePrice(Integer roomId, LocalDate date) {
    try {
        // 步骤1：获取房型信息（新增）
        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房间 {} 不存在", roomId);
            return BigDecimal.ZERO;
        }
        
        // 步骤2：获取日期类型
        DateTypeEnum dateType = chineseCalendarService.getDateType(date);
        
        // 步骤3：查询价格策略
        List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
        
        // 步骤4：特价策略优先处理（新增）
        BigDecimal specialPrice = findSpecialOfferPrice(strategies);
        if (specialPrice != null && specialPrice.compareTo(BigDecimal.ZERO) > 0) {
            log.info("房间 {} 日期 {} 使用特价策略，价格：{}", roomId, date, specialPrice);
            return specialPrice;
        }
        
        // 步骤5：周末不加价处理（新增）
        if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
            if (dateType == DateTypeEnum.WEEKEND) {
                BigDecimal workdayPrice = findWorkdayPrice(strategies);
                if (workdayPrice != null && workdayPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("房间 {} 日期 {} 启用周末不加价，使用工作日价格：{}", roomId, date, workdayPrice);
                    return workdayPrice;
                }
            }
        }
        
        // 步骤6：原有价格计算逻辑（保持不变）
        return strategies.stream()
            .filter(s -> matchesDateType(s, dateType, date))
            .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
            .map(HotelRoomPriceStrategy::getPriceValue)
            .orElse(BigDecimal.ZERO);
            
    } catch (Exception e) {
        log.error("计算价格失败，房间ID: {}, 日期: {}", roomId, date, e);
        return BigDecimal.ZERO;
    }
}
```

### 2. 商品名称生成升级

**修改文件：** `HotelProductSyncServiceImpl.generateProductName()` 方法

**当前格式**: `{商户名}-{房型名}-{日期}`
**修改后格式**: `{商户名}-{房型名}-{日期}[特价][周末不加价]`

```java
private String generateProductName(String hotelName, String roomName, LocalDate checkInDate, HotelRoom room) {
    StringBuilder name = new StringBuilder();
    name.append(String.format("%s-%s-%s", hotelName, roomName, 
                checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
    
    // 检查特价策略（策略类型6）
    if (hasActiveSpecialPrice(room.getId(), checkInDate)) {
        name.append("[特价]");
    }
    
    // 检查周末不加价
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        DateTypeEnum dateType = chineseCalendarService.getDateType(checkInDate);
        if (dateType == DateTypeEnum.WEEKEND) {
            name.append("[周末不加价]");
        }
    }
    
    return name.toString();
}
```

### 3. 商品属性设置升级

**修改文件：** `HotelProductSyncServiceImpl.buildHotelProduct()` 方法

**当前关键词**: `"酒店,住宿,预订," + room.getRoomName()`
**修改后关键词**: 动态添加 `special_offer` 和 `weekend_no_markup` 标记

```java
private void setProductKeywords(Product product, HotelRoom room, LocalDate date) {
    List<String> keywords = new ArrayList<>();
    keywords.add("酒店");
    keywords.add("住宿");
    keywords.add("预订");
    keywords.add(room.getRoomName());
    
    // 特价标记
    if (hasActiveSpecialPrice(room.getId(), date)) {
        keywords.add("special_offer");
    }
    
    // 周末不加价标记
    if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
        keywords.add("weekend_no_markup");
    }
    
    product.setKeyword(String.join(",", keywords));
}
```

### 4. 新增辅助方法

**在HotelProductSyncServiceImpl中新增以下方法：**

```java
/**
 * 检查是否有生效的特价策略
 */
private boolean hasActiveSpecialPrice(Integer roomId, LocalDate date) {
    List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
    return strategies.stream()
        .anyMatch(s -> s.getStrategyType() == 6 && s.getStatus() == 1);
}

/**
 * 查找特价策略价格
 */
private BigDecimal findSpecialOfferPrice(List<HotelRoomPriceStrategy> strategies) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 6 && s.getStatus() == 1)
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}

/**
 * 查找工作日价格
 */
private BigDecimal findWorkdayPrice(List<HotelRoomPriceStrategy> strategies) {
    return strategies.stream()
        .filter(s -> s.getStrategyType() == 1 && s.getStatus() == 1)
        .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
        .map(HotelRoomPriceStrategy::getPriceValue)
        .orElse(null);
}
```

## 🎯 定时任务执行效果示例

### 普通房源商品生成：
- **商品名：** `大粽子的杂货店-精品大床房-2025-01-15`
- **价格计算：** 按原有策略计算（工作日/周末/节假日）
- **关键词：** `酒店,住宿,预订,精品大床房`

### 特价房源商品生成：
- **商品名：** `大粽子的杂货店-精品大床房-2025-01-15[特价]`
- **价格计算：** 优先使用特价策略（strategy_type=6，优先级200）
- **关键词：** `酒店,住宿,预订,精品大床房,special_offer`

### 周末不加价商品生成：
- **商品名：** `大粽子的杂货店-豪华大床房-2025-01-18[周末不加价]`（周六）
- **价格计算：** 周六使用工作日价格策略（strategy_type=1）
- **关键词：** `酒店,住宿,预订,豪华大床房,weekend_no_markup`

### 组合标记：
- **商品名：** `大粽子的杂货店-情侣房-2025-01-19[特价][周末不加价]`
- **价格优先级：** 特价策略(200) > 周末不加价 > 普通策略
- **关键词：** `酒店,住宿,预订,情侣房,special_offer,weekend_no_markup`

## ✅ 实施优势

1. **零侵入性** - 完全基于现有定时任务架构，不破坏原有逻辑
2. **自动化处理** - 定时任务自动识别房型设置，生成对应标记的商品
3. **价格准确性** - 价格计算引擎自动应用特价和周末不加价逻辑
4. **标记清晰** - 商品名称和keyword字段双重标记，便于前端识别
5. **向后兼容** - 现有房型和商品完全不受影响

定时任务详细方案已完成，包含了所有核心修改点和执行效果示例。
