<template>
  <view class="hotel-home">
    <!-- 背景图和搜索区域 -->
    <view class="header-section">
      <!-- 背景图 -->
      <image class="bg-image" src="https://cdn.pixabay.com/photo/2015/04/20/10/16/the-scenery-730884_1280.jpg"
             mode="aspectFill"></image>

      <!-- 日期选择卡片 -->
      <view class="search-card">
        <!-- 搜索和位置信息行 -->
        <view class="search-location-row">
          <!-- 位置信息 -->
          <view class="location-info">
            <text class="location-icon">📍</text>
            <text class="location-text">金梭岛</text>
          </view>

          <!-- 搜索框 -->
          <view class="search-container">
            <text class="search-icon">🔍</text>
            <input
                class="search-input"
                placeholder="搜索酒店名称、房型等"
                v-model="searchKeyword"
                @confirm="handleSearch"
                confirm-type="search"
            />
          </view>
        </view>
        <!-- 日期选择行 -->
        <view class="date-row" @click="showDatePicker">
          <view class="date-item">
            <text class="date-text">{{ formatDate(checkInDate) }}</text>
            <text class="day-text">{{ getDayOfWeek(checkInDate) }}</text>
          </view>

          <view class="date-item">
            <text class="date-text">{{ formatDate(checkOutDate) }}</text>
            <text class="day-text">{{ getDayOfWeek(checkOutDate) }}</text>
          </view>

          <view class="nights-info">
            <text class="nights-text">共{{ nightsCount }}晚</text>
          </view>
        </view>

        <!-- 筛选条件行 -->
        <view class="filter-row">
          <!-- 房型筛选 -->
          <view class="filter-item" @click="showRoomTypeFilter">
            <text class="filter-label">房型</text>
            <text class="filter-value">{{ selectedRoomType || '全部' }}</text>
            <text class="filter-arrow">▼</text>
          </view>

          <!-- 星级筛选 -->
          <view class="filter-item" @click="showStarLevelFilter">
            <text class="filter-label">星级</text>
            <text class="filter-value">{{ selectedStarLevel ? selectedStarLevel + '星' : '全部' }}</text>
            <text class="filter-arrow">▼</text>
          </view>

          <!-- 价格筛选 -->
          <view class="filter-item" @click="showPriceFilter">
            <text class="filter-label">价格</text>
            <text class="filter-value">{{ priceFilterText }}</text>
            <text class="filter-arrow">▼</text>
          </view>

          <!-- 排序 -->
          <view class="filter-item" @click="showSortFilter">
            <text class="filter-label">排序</text>
            <text class="filter-value">{{ sortTypeText }}</text>
            <text class="filter-arrow">▼</text>
          </view>
        </view>

        <!-- 推荐位置标签 -->
        <view class="location-tags">
          <view
              class="tag-item"
              v-for="location in recommendLocations"
              :key="location"
              @click="selectLocation(location)"
          >
            {{ location }}
          </view>
        </view>

        <!-- 搜索按钮 -->
        <button class="search-button" @click="searchHotels">开始搜索</button>
      </view>
    </view>

    <!-- 住宿灵感区域 -->
    <view class="inspiration-section">
      <view class="section-title">住宿灵感</view>

      <!-- 2x2网格布局 -->
      <view class="inspiration-grid">
        <!-- 第一行 -->
        <view class="grid-row">
          <!-- 特价房源 -->
          <view class="inspiration-card card-large" @click="goToSpecialOffers">
            <image
                class="card-bg-image"
                src="https://images.unsplash.com/photo-1618773928121-c32242e63f39?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8aG90ZWx8ZW58MHx8MHx8fDA%3D"
                mode="aspectFill"
            ></image>
            <view class="card-overlay card-overlay-orange"></view>
            <view class="card-content">
              <text class="card-title">特价房源</text>
              <text class="card-subtitle">随时退 过期退</text>
            </view>
          </view>

          <!-- 周末不加价 -->
          <view class="inspiration-card card-small" @click="goToWeekendDeals">
            <image
                class="card-bg-image"
                src="https://plus.unsplash.com/premium_photo-1663126637580-ff22a73f9bfc?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8aG90ZWwlMjBiZWRyb29tfGVufDB8fDB8fHww"
                mode="aspectFill"
            ></image>
            <view class="card-overlay card-overlay-blue"></view>
            <view class="card-content">
              <text class="card-title">周末不加价</text>
              <text class="card-subtitle">平价玩到嗨</text>
            </view>
          </view>
        </view>

        <!-- 第二行 -->
        <view class="grid-row">
          <!-- 龙宫市房源 -->
          <view class="inspiration-card card-small" @click="goToLocalHotels">
            <image
                class="card-bg-image"
                src="https://images.unsplash.com/photo-1562438668-bcf0ca6578f0?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bHV4dXJ5JTIwaG90ZWwlMjByb29tfGVufDB8fDB8fHww"
                mode="aspectFill"
            ></image>
            <view class="card-overlay card-overlay-teal"></view>
            <view class="card-content">
              <text class="card-title">龙宫市房源</text>
              <text class="card-subtitle">龙宫市精房源</text>
            </view>
          </view>

          <!-- 更多选择 -->
          <!--          <view class="inspiration-card card-small" @click="goToMoreOptions">-->
          <!--            <image-->
          <!--                class="card-bg-image"-->
          <!--                src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?fm=jpg&q=60&w=3000"-->
          <!--                mode="aspectFill"-->
          <!--            ></image>-->
          <!--            <view class="card-overlay card-overlay-purple"></view>-->
          <!--            <view class="card-content">-->
          <!--              <text class="card-title">更多选择</text>-->
          <!--              <text class="card-subtitle">发现更多住宿</text>-->
          <!--            </view>-->
          <!--          </view>-->
        </view>
      </view>
    </view>


    <!-- 酒店列表区域 -->
    <view class="hotels-section">
      <view class="hotel-item" v-for="hotel in hotelList" :key="hotel.hotelId" @click="goToHotelDetail(hotel)">
        <view class="hotel-image-container">
          <image
              class="hotel-image"
              :src="hotel.mainImage"
              mode="aspectFill"
          ></image>
          <view class="favorite-btn">
            <uni-icons type="heart" size="20" color="#fff"></uni-icons>
          </view>
        </view>

        <view class="hotel-info">
          <view class="hotel-header">
            <text class="hotel-name">{{ hotel.hotelName }}</text>
            <view class="rating">
              <uni-rate :value="hotel.score / 2" size="12" readonly></uni-rate>
              <text class="score">{{ hotel.score }}分</text>
            </view>
          </view>

          <!-- 酒店地址和距离 -->
          <view class="hotel-location" v-if="hotel.address">
            <text class="address">{{ hotel.address }}</text>
            <text class="distance" v-if="hotel.distance && hotel.distance !== '0'">{{ hotel.distance }}km</text>
          </view>

          <!-- 酒店介绍 -->
          <view class="hotel-intro" v-if="hotel.intro">
            <text class="intro-text">{{ hotel.intro }}</text>
          </view>

          <!-- 房型数量 -->
          <view class="hotel-room-info" v-if="hotel.roomTypeCount">
            <text class="room-count">{{ hotel.roomTypeCount }}种房型</text>
          </view>

          <view class="hotel-footer">
            <view class="price-info">
              <text class="price">¥{{ hotel.minPrice }}</text>
              <text class="price-unit">起</text>
            </view>
            <button class="book-btn" @click.stop="quickBook(hotel)">立即预订</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 房型筛选弹窗 -->
    <u-popup v-model="roomTypeFilterVisible" mode="bottom" border-radius="20" :closeable="true">
      <view class="filter-popup">
        <view class="filter-title">选择房型</view>
        <view class="filter-options">
          <view class="filter-option" :class="{ active: !selectedRoomType }" @click="selectRoomType('')">
            <text>全部房型</text>
          </view>
          <view class="filter-option" v-for="roomType in roomTypeOptions" :key="roomType"
                :class="{ active: selectedRoomType === roomType }" @click="selectRoomType(roomType)">
            <text>{{ roomType }}</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 星级筛选弹窗 -->
    <u-popup v-model="starLevelFilterVisible" mode="bottom" border-radius="20" :closeable="true">
      <view class="filter-popup">
        <view class="filter-title">选择星级</view>
        <view class="filter-options">
          <view class="filter-option" :class="{ active: !selectedStarLevel }" @click="selectStarLevel(null)">
            <text>全部星级</text>
          </view>
          <view class="filter-option" v-for="star in [1,2,3,4,5]" :key="star"
                :class="{ active: selectedStarLevel === star }" @click="selectStarLevel(star)">
            <text>{{ star }}星</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 价格筛选弹窗 -->
    <u-popup v-model="priceFilterVisible" mode="bottom" border-radius="20" :closeable="true">
      <view class="filter-popup">
        <view class="filter-title">价格区间</view>
        <view class="price-range-container">
          <view class="price-input-group">
            <input class="price-input" v-model="minPrice" placeholder="最低价" type="number"/>
            <text class="price-separator">-</text>
            <input class="price-input" v-model="maxPrice" placeholder="最高价" type="number"/>
          </view>
          <button class="confirm-price-btn" @click="confirmPriceFilter">确定</button>
        </view>
      </view>
    </u-popup>

    <!-- 排序筛选弹窗 -->
    <u-popup v-model="sortFilterVisible" mode="bottom" border-radius="20" :closeable="true">
      <view class="filter-popup">
        <view class="filter-title">排序方式</view>
        <view class="filter-options">
          <view class="filter-option" v-for="sort in sortOptions" :key="sort.value"
                :class="{ active: selectedSortType === sort.value }" @click="selectSortType(sort.value)">
            <text>{{ sort.label }}</text>
          </view>
        </view>
      </view>
    </u-popup>

  </view>
</template>

<script>
import {getHotelList} from '@/nxTemp/apis/hotel.js'

export default {
  name: 'HotelHome',
  data() {
    return {
      checkInDate: '',
      checkOutDate: '',
      today: '',
      recommendLocations: ['推荐位置', '推荐位置', '推荐位置', '推荐位置'],
      hotelList: [],
      loading: false,
      searchKeyword: '',

      // 筛选条件
      selectedRoomType: '',
      selectedStarLevel: null,
      selectedSortType: null,
      minPrice: '',
      maxPrice: '',

      // 弹窗显示状态
      roomTypeFilterVisible: false,
      starLevelFilterVisible: false,
      priceFilterVisible: false,
      sortFilterVisible: false,

      // 选项数据
      roomTypeOptions: ['大床房', '双人间', '套房', '家庭房', '商务房', '豪华房'],
      sortOptions: [
        {label: '默认排序', value: null},
        {label: '价格升序', value: 1},
        {label: '价格降序', value: 2},
        {label: '距离优先', value: 3},
        {label: '评分优先', value: 4}
      ]
    }
  },

  computed: {
    nightsCount() {
      if (!this.checkInDate || !this.checkOutDate) return 1
      const checkIn = new Date(this.checkInDate)
      const checkOut = new Date(this.checkOutDate)
      return Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24))
    },

    priceFilterText() {
      if (this.minPrice && this.maxPrice) {
        return `¥${this.minPrice}-${this.maxPrice}`
      } else if (this.minPrice) {
        return `¥${this.minPrice}起`
      } else if (this.maxPrice) {
        return `¥${this.maxPrice}内`
      }
      return '价格'
    },

    sortTypeText() {
      const sort = this.sortOptions.find(s => s.value === this.selectedSortType)
      return sort ? sort.label : '排序'
    }
  },

  onLoad() {
    this.initDates()
    this.loadHotelList()
    // this.initMockData()
  },

  methods: {
    initDates() {
      const today = new Date()
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

      this.today = this.formatDateForPicker(today)
      this.checkInDate = this.formatDateForPicker(today)
      this.checkOutDate = this.formatDateForPicker(tomorrow)
    },

    formatDateForPicker(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    getDayOfWeek(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return days[date.getDay()]
    },

    showDatePicker() {
      // 跳转到日期选择页面
      uni.navigateTo({
        url: `/pages/shopping/hotel/date-select?checkInDate=${this.checkInDate}&checkOutDate=${this.checkOutDate}`
      })
    },

    handleSearch() {
      // 执行搜索（支持空关键词搜索）
      this.loadHotelList()

      // 滚动到酒店列表区域
      uni.pageScrollTo({
        selector: '.hotels-section',
        duration: 300
      })
    },

    selectLocation(location) {
      console.log('选择位置:', location)
      // 这里可以实现位置筛选逻辑
    },

    async searchHotels() {
      this.loading = true
      try {
        await this.loadHotelList()
        // 滚动到酒店列表区域
        uni.pageScrollTo({
          selector: '.hotels-section',
          duration: 300
        })
      } catch (error) {
        console.error('搜索酒店失败:', error)
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    async loadHotelList() {
      this.loading = true

      try {
        const params = {
          page: 1,
          limit: 10,
          keyword: this.searchKeyword,
          roomType: this.selectedRoomType,
          starLevel: this.selectedStarLevel,
          minPrice: this.minPrice || null,
          maxPrice: this.maxPrice || null,
          sortType: this.selectedSortType,
          location: this.searchKeyword || '金梭岛',
          checkInDate: this.checkInDate,
          checkOutDate: this.checkOutDate
        }

        console.log('酒店搜索参数:', params)

        const response = await getHotelList(params)
        const {code, data} = response.data

        if (code === 200 && data?.list) {
          this.hotelList = data.list
          console.log('酒店列表加载成功:', this.hotelList)
        } else {
          this.hotelList = []
          console.warn('酒店列表数据异常:', response)
        }
      } catch (error) {
        console.error('加载酒店列表失败:', error)
        this.hotelList = []
        uni.showToast({
          title: '加载酒店列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },


    goToHotelDetail(hotel) {
      uni.navigateTo({
        url: `/pages/shopping/hotel/detail?hotelId=${hotel.hotelId}&checkInDate=${this.checkInDate}&checkOutDate=${this.checkOutDate}`
      })
    },

    quickBook(hotel) {
      this.goToHotelDetail(hotel)
    },

    // 筛选相关方法
    showRoomTypeFilter() {
      this.roomTypeFilterVisible = true
    },

    showStarLevelFilter() {
      this.starLevelFilterVisible = true
    },

    showPriceFilter() {
      this.priceFilterVisible = true
    },

    showSortFilter() {
      this.sortFilterVisible = true
    },

    selectRoomType(roomType) {
      this.selectedRoomType = roomType
      this.roomTypeFilterVisible = false
      this.loadHotelList()
    },

    selectStarLevel(starLevel) {
      this.selectedStarLevel = starLevel
      this.starLevelFilterVisible = false
      this.loadHotelList()
    },

    selectSortType(sortType) {
      this.selectedSortType = sortType
      this.sortFilterVisible = false
      this.loadHotelList()
    },

    confirmPriceFilter() {
      this.priceFilterVisible = false
      this.loadHotelList()
    },

    // 住宿灵感卡片点击事件
    goToSpecialOffers() {
      // 跳转到特价房源页面
      uni.showToast({
        title: '特价房源功能开发中',
        icon: 'none'
      })
    },

    goToWeekendDeals() {
      // 跳转到周末不加价页面
      uni.showToast({
        title: '周末不加价功能开发中',
        icon: 'none'
      })
    },

    goToLocalHotels() {
      // 跳转到龙宫市房源页面
      uni.showToast({
        title: '龙宫市房源功能开发中',
        icon: 'none'
      })
    },

    // goToMoreOptions() {
    //   // 跳转到更多选择页面
    //   uni.showToast({
    //     title: '更多选择功能开发中',
    //     icon: 'none'
    //   })
    // },

    // 初始化模拟数据
    // initMockData() {
    //   this.hotelList = [
    //     {
    //       hotelId: 1,
    //       hotelName: '金梭岛度假酒店',
    //       mainImage: '/static/images/hotel/hotel1.jpg',
    //       score: 9.2,
    //       tags: ['海景', '可退改', '有早餐'],
    //       minPrice: 588
    //     },
    //     {
    //       hotelId: 2,
    //       hotelName: '大理古城精品民宿',
    //       mainImage: '/static/images/hotel/hotel2.jpg',
    //       score: 8.8,
    //       tags: ['古城', '特色', '免费WiFi'],
    //       minPrice: 298
    //     },
    //     {
    //       hotelId: 3,
    //       hotelName: '洱海边度假别墅',
    //       mainImage: '/static/images/hotel/hotel3.jpg',
    //       score: 9.5,
    //       tags: ['洱海', '别墅', '私人泳池'],
    //       minPrice: 1288
    //     }
    //   ]
    // }
  }
}
</script>

<style lang="scss" scoped>
.hotel-home {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 背景图和搜索区域 */
.header-section {
  position: relative;
  height: 770rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #7B68EE 50%, #9370DB 100%);

  .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }


  .search-card {
    position: absolute;
    bottom: -120rpx;
    left: 30rpx;
    right: 30rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 50rpx 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    z-index: 10;

    .search-location-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40rpx;
      gap: 30rpx;
    }

    .location-info {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .location-icon {
        font-size: 28rpx;
        color: #ff6b35;
        margin-right: 16rpx;
      }

      .location-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }
    }

    .search-container {
      display: flex;
      align-items: center;
      background: #f0f0f0;
      border-radius: 40rpx;
      padding: 20rpx 30rpx;
      min-height: 60rpx;
      border: 2rpx solid #ddd;
      flex: 1;
      box-sizing: border-box;

      .search-icon {
        font-size: 28rpx;
        color: #999;
        margin-right: 16rpx;
      }

      .search-input {
        flex: 1;
        font-size: 26rpx;
        color: #333;
        background: transparent;
        border: none;
        line-height: 1.4;
      }
    }

    .date-row {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      padding: 20rpx 0;

      .date-item {
        flex: 1;
        text-align: center;

        .date-text {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
          display: block;
        }

        .day-text {
          font-size: 24rpx;
          color: #666;
        }
      }

      .nights-info {
        .nights-text {
          font-size: 24rpx;
          color: #666;
        }
      }
    }

    .location-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-bottom: 40rpx;

      .tag-item {
        padding: 12rpx 24rpx;
        background: #f5f5f5;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #666;
        border: 1rpx solid #e0e0e0;
      }
    }

    .search-button {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #ff6b35, #ff8f65);
      border-radius: 44rpx;
      border: none;
      font-size: 32rpx;
      color: #fff;
      font-weight: bold;
    }
  }
}

/* 住宿灵感区域 */
.inspiration-section {
  padding: 30rpx;
  margin: 180rpx 30rpx 40rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }

  .inspiration-grid {
    .grid-row {
      display: flex;
      gap: 24rpx;
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .inspiration-card {
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

      &.card-large {
        flex: 2;
        height: 240rpx;
      }

      &.card-small {
        flex: 1;
        height: 240rpx;
      }

      .card-bg-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }

      .card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        z-index: 2;

        &.card-overlay-orange {
          background: rgba(255, 107, 53, 0.7);
        }

        &.card-overlay-blue {
          background: rgba(59, 130, 246, 0.7);
        }

        &.card-overlay-teal {
          background: rgba(20, 184, 166, 0.7);
        }

        &.card-overlay-purple {
          background: rgba(147, 112, 219, 0.7);
        }
      }

      .card-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 24rpx;
        z-index: 3;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        height: 100%;

        .card-title {
          font-size: 28rpx;
          color: #fff;
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .card-subtitle {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

/* 酒店列表区域 */
.hotels-section {
  padding: 0 30rpx;
  margin-bottom: 40rpx;

  .hotel-item {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .hotel-image-container {
      position: relative;
      height: 400rpx;
      background: linear-gradient(135deg, #ddd, #f0f0f0);

      .hotel-image {
        width: 100%;
        height: 100%;
      }

      .favorite-btn {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        width: 60rpx;
        height: 60rpx;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .hotel-info {
      padding: 30rpx;

      .hotel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .hotel-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          flex: 1;
        }

        .rating {
          display: flex;
          align-items: center;
          gap: 10rpx;

          .score {
            font-size: 24rpx;
            color: #666;
          }
        }
      }

      .hotel-tags {
        margin-bottom: 30rpx;

        .tag {
          display: inline-block;
          padding: 8rpx 16rpx;
          background: #f0f0f0;
          border-radius: 20rpx;
          font-size: 22rpx;
          color: #666;
          margin-right: 16rpx;
        }
      }

      .hotel-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .price-info {
          .price {
            font-size: 36rpx;
            font-weight: bold;
            color: #ff6b35;
          }

          .price-unit {
            font-size: 24rpx;
            color: #666;
          }
        }

        .book-btn {
          padding: 16rpx 32rpx;
          background: #ff6b35;
          border-radius: 30rpx;
          border: none;
          font-size: 28rpx;
          color: #fff;
        }
      }
    }
  }
}

/* 筛选条件样式 */
.filter-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  margin: 0 5rpx;
}

.filter-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.filter-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.filter-arrow {
  font-size: 20rpx;
  color: #999;
  margin-top: 5rpx;
}

/* 弹窗样式 */
.filter-popup {
  padding: 40rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 40rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-option {
  flex: 0 0 calc(50% - 10rpx);
  padding: 25rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  text-align: center;

  &.active {
    background: #667eea;
    color: white;
  }
}

/* 价格筛选样式 */
.price-range-container {
  padding: 20rpx 0;
}

.price-input-group {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.price-input {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.price-separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #666;
}

.confirm-price-btn {
  width: 100%;
  height: 80rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}
</style>
