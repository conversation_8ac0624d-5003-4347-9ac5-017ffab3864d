<template>
  <view class="me-container">
    <image class="me-bgimg" :src="allImgList.me_bg || ''" mode="aspectFill"></image>
    <view class="me-content">
      <!-- 用户信息 -->
      <view
          class="user-info-panel animation-fade"
          @click="navigateTo(hasLogin ? '/pages/me/userInfo' : '/pages/login/login')">
        <image class="user-avatar" :src="userAvatar || allImgList.defaultAvatar" mode="aspectFill"></image>
        <view class="user-info">
          <view v-if="hasLogin" class="user-name">{{ userInfo.nickname }}</view>
          <view v-if="hasLogin" class="user-desc">
            点击编辑用户资料
            <image class="arrow-icon" src="@/static/images/icons/arrow-right.png"></image>
          </view>
          <view v-else class="login-text">未登录，去登录</view>
        </view>
        <view class="user-audit">
          <img class="audit-arrow" src="@/static/images/me/order-verification.png" alt="" />
          <view class="audit-text">订单核验</view>
        </view>
      </view>

      <!-- 龙宫币积分显示 -->
      <view class="coins-panel">
        <view
            class="coin-item animation-slide-left"
            :style="{ backgroundImage: `url(${allImgList.cumulativeCoins || ''})` }">
          <view class="coin-label">累计龙宫币</view>
          <view class="coin-value">{{ current }}</view>
        </view>
        <view
            class="coin-item animation-slide-right"
            :style="{ backgroundImage: `url(${allImgList.availableCoins || ''})` }">
          <view class="coin-label">可用龙宫币</view>
          <view class="coin-value">{{ surplus }}</view>
          <view class="coin-text">
            <view class="coin-text-label">去兑换</view>
          </view>
        </view>
      </view>

      <!-- 龙宫币记录 -->
      <view class="function-panel">
        <view class="function-tabs">
          <view
              v-for="(tab, index) in coinTabs"
              :key="index"
              class="function-tab-item animation-slide-bottom"
              :style="{ animationDelay: 0.3 + index * 0.15 + 's' }"
              @click="routeTo(tab)">
            <image :src="allImgList[tab.icon] || ''" class="function-icon"></image>
            <text class="function-text">{{ tab.name }}</text>
          </view>
        </view>
      </view>

      <!-- 我的订单 -->
      <view v-if="hasLogin" class="order-panel">
        <view class="order-title">
          <text>我的订单</text>
          <view class="order-more" @click="showAllOrders = !showAllOrders">
            <text>{{ showAllOrders ? '收起' : '查看全部' }}</text>
            <image src="@/static/images/icons/arrow-gray.png"
                   :class="{ 'arrow-up': showAllOrders }"
                   class="order-arrow"></image>
          </view>
        </view>

        <!-- 订单状态快捷入口 -->
        <view class="order-status-tabs">
          <view
              v-for="status in orderStatusTabs"
              :key="status.value"
              class="order-status-item"
              @click="filterOrdersByStatus(status.value)">
            <view class="status-icon">{{ status.icon }}</view>
            <text class="status-text">{{ status.label }}</text>
            <view v-if="status.count > 0" class="status-badge">{{ status.count }}</view>
          </view>
        </view>

        <!-- 订单列表 -->
        <view v-if="showAllOrders" class="order-list">
          <view v-if="orderList.length === 0" class="empty-orders">
            <text>暂无订单</text>
          </view>
          <view v-else class="order-items">
            <view
                v-for="order in displayOrderList"
                :key="order.id"
                class="order-item"
                @click="goToOrderDetail(order.orderNo)">

              <!-- 订单头部 -->
              <view class="order-header">
                <view class="merchant-name">{{ order.merName }}</view>
                <view class="order-status" :class="getStatusClass(order.status)">
                  {{ getStatusText(order.status) }}
                </view>
              </view>

              <!-- 商品信息 -->
              <view class="order-product">
                <image
                    v-if="order.orderInfoList && order.orderInfoList[0]"
                    :src="order.orderInfoList[0].image"
                    class="product-image"
                    mode="aspectFill">
                </image>
                <view class="product-info">
                  <view class="product-name">
                    {{
                      order.orderInfoList && order.orderInfoList[0] ? order.orderInfoList[0].productName : '商品信息'
                    }}
                  </view>
                  <view class="product-price">¥{{ order.payPrice }}</view>
                </view>
              </view>

              <!-- 订单操作 -->
              <view class="order-actions">
                <!-- 待支付 -->
                <template v-if="order.status === 0 && !order.paid">
                  <view class="action-btn secondary" @click.stop="cancelOrder(order.orderNo)">取消</view>
                  <view class="action-btn primary" @click.stop="goToPay(order.orderNo)">支付</view>
                </template>

                <!-- 待核销 -->
                <template v-if="order.status === 3">
                  <view class="action-btn primary" @click.stop="showVerifyCode(order)">查看核销码</view>
                </template>

                <!-- 待收货 -->
                <template v-if="order.status === 4">
                  <view class="action-btn primary" @click.stop="confirmReceive(order.orderNo)">确认收货</view>
                </template>

                <!-- 已完成/已取消 - 可删除 -->
                <template v-if="order.status === 6 || order.status === 9">
                  <view class="action-btn secondary" @click.stop="deleteOrder(order.orderNo)">删除</view>
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 核销码弹窗 -->
      <u-popup
          :show="verifyCodeModal"
          mode="center"
          border-radius="20"
          @close="verifyCodeModal = false"
      >
        <view class="verify-popup-content">
          <!-- 头部 -->
          <view class="verify-header">
            <view class="verify-title">核销码</view>
            <u-icon name="close" size="24" color="#999" @click="verifyCodeModal = false"></u-icon>
          </view>

          <!-- 核销码主体 -->
          <view class="verify-main">
            <!-- 二维码区域 -->
            <view class="qr-section">
              <view class="qr-container">
                <qr-code
                    v-if="currentVerifyCode && currentVerifyCode !== '暂无核销码'"
                    :text="currentVerifyCode"
                    :size="180"
                    canvas-id="verify-qrcode"
                    @success="onQRCodeSuccess"
                    @error="onQRCodeError"
                ></qr-code>
                <view v-else class="qr-placeholder">
                  <view class="placeholder-box">
                    <u-icon name="qrcode" size="60" color="#ccc"></u-icon>
                    <text class="placeholder-text">暂无核销码</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 核销码信息 -->
            <view class="code-info">
              <view class="code-title">核销码</view>
              <view class="code-value">{{ currentVerifyCode || '暂无核销码' }}</view>
              <view class="code-actions">
                <view class="action-btn" @click="copyVerifyCode">
                  <u-icon name="copy" size="16" color="#007aff"></u-icon>
                  <text>复制</text>
                </view>
              </view>
            </view>

            <!-- 提示信息 -->
            <view class="tips">
              <text class="tip-text">请向商家出示此核销码完成核销</text>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="verify-footer">
            <u-button type="primary" text="我知道了" @click="verifyCodeModal = false"></u-button>
          </view>
        </view>
      </u-popup>

      <!-- 反馈投诉 -->
      <view class="feedback-panel">
        <view class="feedback-title">
          <text>反馈投诉</text>
          <image src="@/static/images/icons/bg-img.png" mode="aspectFill" alt="" />
        </view>
        <view class="feedback-list">
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.6 + 's' }"
              @click="navigateTo('/pages/complaints/complaints')"
          >
            <img class="feedback-icon" src="@/static/images/icons/icon-complaint.png" alt="" />
            <view class="feedback-text">投诉建议</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              v-if="$permission.hasPermission('wx:reply')"
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.6 + 's' }"
              @click="navigateTo('/pages/complaints/complaintList')"
          >
            <img class="feedback-icon" src="@/static/images/icons/icon-reply.png" style="width: 40rpx;height: 40rpx;margin-left: 2rpx; margin-right: 8rpx" alt="" />
            <view class="feedback-text">回复建议</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.7 + 's' }"
              @click="navigateTo('/pages/me/questionnaire')">
            <img class="feedback-icon" src="@/static/images/icons/icon-questionnaire.png" alt="" />
            <view class="feedback-text">景区问卷调查</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.8 + 's' }"
              @click="callPhone">
            <img class="feedback-icon" src="@/static/images/icons/icon-phone.png" alt="" />
            <view class="feedback-text">
              <view>投诉电话</view>
              <view>{{ phoneNumber }}</view>
            </view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.9 + 's' }"
              @click="navigateTo('/pages/me/chamberlain')">
            <img class="feedback-icon" src="@/static/images/icons/icon-butler.png" alt="" />
            <view class="feedback-text">金梭专属小管家</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.9 + 's' }"
              @click="navigateTo('/pages/shopping/collect/index')">
            <img class="feedback-icon" src="@/static/images/me/collect.png" alt="" />
            <view class="feedback-text">我的收藏</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.9 + 's' }"
              @click="navigateTo('/pages/address/index')">
            <img class="feedback-icon" src="@/static/images/me/delivery_address.png" alt="" />
            <view class="feedback-text">收货地址</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
          <view
              class="feedback-item animation-slide-bottom"
              :style="{ animationDelay: 0.9 + 's' }"
              @click="navigateTo('/pages/distribution/index')">
            <img class="feedback-icon" src="@/static/images/icons/icon-butler.png" alt="" />
            <view class="feedback-text">分销赚钱</view>
            <img class="feedback-arrow" src="@/static/images/icons/arrow-gray.png" alt="" />
          </view>
        </view>
      </view>
      <view v-if="hasLogin" class="logout-btn" @click="logoutShow = true"> 退出登录</view>
    </view>
    <u-modal
        :show="logoutShow"
        title="退出登录"
        @confirm="logout"
        @cancel="logoutShow = false"
        showCancelButton>
      <template #default>
        <view class="x-c text-red">确定退出登录吗？</view>
      </template>
    </u-modal>
  </view>
</template>

<script>
import {mapActions, mapGetters, mapState} from "vuex";
import {BASE_IMG_URL} from "@/env.js";
import {getCoinRecordNumber} from "@/nxTemp/apis/common";
import {
  cancelOrder as cancelOrderApi,
  confirmReceive,
  deleteOrder,
  getOrderDetail,
  getOrderList
} from "@/nxTemp/apis/shopping.js";
import QrCode from "@/components/qr-code/qr-code.vue";

export default {
  components: {
    QrCode
  },
  data() {
    return {
      logoutShow: false,
      // 龙宫币记录
      coinTabs: [
        {
          name: "龙宫币记录",
          icon: "coinRecord_icon",
          url: "/pages/me/coinRecord",
        },
        {
          name: "龙宫币规则",
          icon: "coinRules_icon",
          url: "/pages/public/imagPage",
          imageType: "coinRules",
        },
        {
          name: "兑换规则",
          icon: "exchangeRules_icon",
          url: "/pages/public/imagPage",
          imageType: "exchangeRules",
        },
      ],
      // 龙宫币数量
      current: 0,
      surplus: 0,

      // 投诉电话
      phoneNumber: "0872-3067905",

      // 订单相关
      showAllOrders: true,
      orderList: [],
      currentOrderStatus: -1, // 当前筛选的订单状态
      verifyCodeModal: false,
      currentVerifyCode: '',
      orderStatusTabs: [
        {label: '待支付', value: 0, icon: '\uD83D\uDCB0', count: 0},
        {label: '待核销', value: 3, icon: '\uD83C\uDF9F', count: 0},
        {label: '待收货', value: 4, icon: '\uD83D\uDCE6', count: 0},
        {label: '已完成', value: 6, icon: '\u2705', count: 0}
      ]
    };
  },
  onShow() {
    this.getCoinRecordNumber();
    if (this.hasLogin) {
      this.initOrderData();
    }
  },
  computed: {
    ...mapState(["userInfo", "allImgList"]),
    ...mapGetters(["hasLogin"]),
    userAvatar() {
      return this.userInfo.avatar
          ? BASE_IMG_URL + this.userInfo.avatar
          : "/static/images/icons/avatar.png";
    },
    // 显示的订单列表（根据状态筛选）
    displayOrderList() {
      if (this.currentOrderStatus === -1) {
        return this.orderList.slice(0, 3); // 显示最近3个订单
      }
      return this.orderList.filter(order => order.status === this.currentOrderStatus).slice(0, 3);
    }
  },
  methods: {
    ...mapActions(["logoutFun"]),
    // 获取龙宫币数量
    async getCoinRecordNumber() {
      try {
        const res = await getCoinRecordNumber();
        const { data = {}, code, message } = res.data;
        if (code === 200) {
          this.current = data.current || 0;
          this.surplus = data.surplus || 0;
        } else {
          this.current = 0;
          this.surplus = 0;
        }
      } catch (error) {
        this.current = 0;
        this.surplus = 0;
        console.log("获取龙宫币数量失败", error);
      }
    },
    // 退出登录
    logout() {
      this.current = 0;
      this.surplus = 0;
      this.logoutFun();
      this.logoutShow = false;
    },
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({
        url: url,
      });
    },
    // 页面跳转
    routeTo(item) {
      uni.$u.route({
        url: item.url,
        params: {
          imagType: item.imageType || "",
          isDesc: item.imageType === "coinRules" ? '1' : '0',
        },
      });
    },
    // 拨打电话
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: this.phoneNumber,
      });
    },

    // ==================== 订单相关方法 ====================

    // 初始化订单数据
    async initOrderData() {
      await this.getOrderList();
      this.updateOrderStatusCount();
    },

    // 获取订单列表
    async getOrderList() {
      try {
        const res = await getOrderList({
          status: -1, // 获取所有状态的订单
          page: 1,
          limit: 10
        });
        // 处理API响应数据
        if (res.data && res.data.code === 200) {
          this.orderList = res.data.data.list || [];
        } else {
          this.orderList = res.list || res.data?.list || [];
        }
      } catch (err) {
        console.error('获取订单列表失败:', err);
        this.orderList = [];
      }
    },

    // 更新订单状态统计
    updateOrderStatusCount() {
      this.orderStatusTabs.forEach(tab => {
        tab.count = this.orderList.filter(order => order.status === tab.value).length;
      });
    },

    // 按状态筛选订单
    filterOrdersByStatus(status) {
      this.currentOrderStatus = status;
    },

    // 获取订单状态样式
    getStatusClass(status) {
      const statusMap = {
        0: 'status-unpaid',
        1: 'status-unshipped',
        3: 'status-unverified',
        4: 'status-unreceived',
        6: 'status-completed',
        9: 'status-cancelled'
      };
      return statusMap[status] || '';
    },

    // 获取订单状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        3: '待核销',
        4: '待收货',
        6: '已完成',
        9: '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    // 跳转到订单详情
    goToOrderDetail(orderNo) {
      uni.navigateTo({
        url: `/pages/me/order/detail?orderNo=${orderNo}`
      });
    },

    // 获取订单详情
    async getOrderDetail(orderNo) {
      try {
        const res = await getOrderDetail(orderNo);
        if (res.data && res.data.code === 200) {
          return res.data.data;
        } else {
          return res.data || res;
        }
      } catch (err) {
        console.error('获取订单详情失败:', err);
        this.$u.toast('获取订单详情失败');
        return null;
      }
    },

    // 跳转到支付页面
    goToPay(orderNo) {
      uni.navigateTo({
        url: `/pages/payment/index?orderNo=${orderNo}`
      });
    },

    // 显示核销码
    async showVerifyCode(order) {
      try {
        console.log('显示核销码 - 订单数据:', order);

        // 先尝试从当前订单数据获取核销码
        let verifyCode = null;

        // 检查多种可能的数据结构
        if (order.merchantOrderList && order.merchantOrderList.length > 0) {
          verifyCode = order.merchantOrderList[0].verifyCode;
          console.log('从merchantOrderList获取核销码:', verifyCode);
        } else if (order.verifyCode) {
          verifyCode = order.verifyCode;
          console.log('从order.verifyCode获取核销码:', verifyCode);
        } else if (order.orderInfoList && order.orderInfoList.length > 0) {
          verifyCode = order.orderInfoList[0].verifyCode;
          console.log('从orderInfoList获取核销码:', verifyCode);
        }

        // 如果没有找到核销码，尝试获取订单详情
        if (!verifyCode && order.orderNo) {
          console.log('尝试从订单详情获取核销码...');
          const orderDetail = await this.getOrderDetail(order.orderNo);
          console.log('订单详情数据:', orderDetail);
          if (orderDetail) {
            if (orderDetail.merchantOrderList && orderDetail.merchantOrderList.length > 0) {
              verifyCode = orderDetail.merchantOrderList[0].verifyCode;
              console.log('从订单详情merchantOrderList获取核销码:', verifyCode);
            } else if (orderDetail.verifyCode) {
              verifyCode = orderDetail.verifyCode;
              console.log('从订单详情verifyCode获取核销码:', verifyCode);
            }
          }
        }

        this.currentVerifyCode = verifyCode || '暂无核销码';
        console.log('最终核销码:', this.currentVerifyCode);
        console.log('显示弹窗前 verifyCodeModal:', this.verifyCodeModal);
        this.verifyCodeModal = true;
        console.log('显示弹窗后 verifyCodeModal:', this.verifyCodeModal);

        // 强制更新视图
        this.$forceUpdate();
      } catch (err) {
        console.error('获取核销码失败:', err);
        this.currentVerifyCode = '获取核销码失败';
        this.verifyCodeModal = true;
      }
    },

    // 二维码生成成功回调
    onQRCodeSuccess(data) {
      console.log('二维码生成成功:', data);
    },

    // 二维码生成失败回调
    onQRCodeError(error) {
      console.error('二维码生成失败:', error);
    },

    // 复制核销码
    copyVerifyCode() {
      if (!this.currentVerifyCode || this.currentVerifyCode === '暂无核销码') {
        this.$u.toast('暂无可复制的核销码');
        return;
      }

      uni.setClipboardData({
        data: this.currentVerifyCode,
        success: () => {
          this.$u.toast('核销码已复制到剪贴板');
        },
        fail: () => {
          this.$u.toast('复制失败');
        }
      });
    },

    // 取消订单
    async cancelOrder(orderNo) {
      try {
        const confirm = await this.$u.modal.confirm('确定要取消此订单吗？');
        if (confirm) {
          const res = await cancelOrderApi(orderNo);
          if (res.data && res.data.code === 200) {
            this.$u.toast('订单已取消');
            this.getOrderList(); // 刷新订单列表
          } else {
            this.$u.toast(res.data?.message || '取消订单失败');
          }
        }
      } catch (err) {
        console.error('取消订单失败:', err);
        this.$u.toast('取消订单失败');
      }
    },

    // 删除订单
    async deleteOrder(orderNo) {
      try {
        const confirm = await this.$u.modal.confirm('确定要删除此订单吗？删除后无法恢复');
        if (confirm) {
          const res = await deleteOrder(orderNo);
          if (res.data && res.data.code === 200) {
            this.$u.toast('订单已删除');
            this.getOrderList(); // 刷新订单列表
          } else {
            this.$u.toast(res.data?.message || '删除订单失败');
          }
        }
      } catch (err) {
        console.error('删除订单失败:', err);
        this.$u.toast('删除订单失败');
      }
    },

    // 确认收货
    async confirmReceive(orderNo) {
      try {
        const confirm = await this.$u.modal.confirm('确定已收到商品吗？');
        if (confirm) {
          const res = await confirmReceive(orderNo);
          if (res.data && res.data.code === 200) {
            this.$u.toast('确认收货成功');
            this.getOrderList(); // 刷新订单列表
          } else {
            this.$u.toast(res.data?.message || '确认收货失败');
          }
        }
      } catch (err) {
        console.error('确认收货失败:', err);
        this.$u.toast('确认收货失败');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.me-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.me-bgimg {
  width: 100%;
  height: 300pt;
}

.me-content {
  position: absolute;
  top: 178rpx;
  left: 0;
  width: 100%;
}

.user-info-panel {
  margin: 0 26rpx;
  height: 154rpx;
  display: flex;
  align-items: center;
  animation-delay: 0.1s;
}

.user-avatar {
  width: 154rpx;
  height: 154rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.user-info {
  margin-left: 22rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.user-name {
  font-size: 46rpx;
  color: #fff;
  margin-bottom: 12rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.user-desc {
  font-size: 22rpx;
  color: #fff;
  background-color: #309bb7;
  padding: 6rpx 20rpx;
  border-radius: 999rpx;
  border: 0.5rpx solid #fff;
  display: inline-flex;
  align-items: center;
  width: auto;
  max-width: max-content;
}

.login-text {
  font-size: 32rpx;
  color: #fff;
}

.arrow-icon {
  width: 8rpx;
  height: 14rpx;
  margin-left: 12rpx;
}

.user-audit {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}

.audit-arrow {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 8rpx;
}

.audit-text {
  font-size: 20rpx;
  color: #fff;
}

/* 龙宫币积分样式 */
.coins-panel {
  margin: 0 30rpx;
  margin-top: 46rpx;
  height: 160rpx;
  display: flex;
  justify-content: space-between;
}

.coin-item {
  width: 48%;
  height: 100%;
  background-size: cover;
  background-position: center;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.coin-label {
  font-size: 24rpx;
  color: #fff;
  margin-bottom: 10rpx;
}

.coin-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.coin-text {
  position: absolute;
  bottom: 20rpx;
  right: 30rpx;
}

.coin-text-label {
  font-size: 20rpx;
  color: #fff;
}

/* 功能面板样式 */
.function-panel {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}

.function-tabs {
  display: flex;
  justify-content: space-around;
}

.function-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.function-text {
  font-size: 24rpx;
  color: #666;
}

/* 订单面板样式 */
.order-panel {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}

.order-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
}

.order-more {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #8e8e93;
}

.order-arrow {
  width: 11rpx;
  height: 19rpx;
  margin-left: 8rpx;
  transition: transform 0.3s;

  &.arrow-up {
    transform: rotate(180deg);
  }
}

.order-status-tabs {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: background-color 0.3s;

  &:active {
    background-color: #f5f5f5;
  }
}

.status-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.status-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff3b30;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

.order-list {
  margin-top: 20rpx;
}

.empty-orders {
  text-align: center;
  padding: 60rpx 0;
  color: #8e8e93;
  font-size: 28rpx;
}

.order-items {
  .order-item {
    background-color: #f8f8f8;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.merchant-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.order-status {
  font-size: 24rpx;

  &.status-unpaid {
    color: #ff9500;
  }

  &.status-unshipped {
    color: #007aff;
  }

  &.status-unverified {
    color: #34c759;
  }

  &.status-unreceived {
    color: #007aff;
  }

  &.status-completed {
    color: #8e8e93;
  }

  &.status-cancelled {
    color: #8e8e93;
  }
}

.order-product {
  display: flex;
  margin-bottom: 20rpx;
}

.product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 28rpx;
  color: #ff3b30;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;

  &.primary {
    background-color: #007aff;
    color: #fff;
  }

  &.secondary {
    background-color: #f0f0f0;
    color: #666;
  }
}

.verify-code-content {
  text-align: center;
  padding: 40rpx 0;
}

.verify-code-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 20rpx;
  letter-spacing: 4rpx;
}

.verify-code-tip {
  font-size: 24rpx;
  color: #8e8e93;
}

/* 反馈投诉面板样式 */
.feedback-panel {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx 0;
}

.feedback-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
  padding-bottom: 35rpx;
  position: relative;
  padding-left: 34rpx;
}

.feedback-title image {
  position: absolute;
  top: 18rpx;
  left: 34rpx;
  width: 42rpx;
  height: 39rpx;
}

.feedback-list {
  padding: 0 34rpx;
}

.feedback-item {
  display: flex;
  height: 117rpx;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  padding: 0;
}

.feedback-item:last-child {
  border-bottom: none;
}

.feedback-icon {
  width: 50rpx;
  height: 50rpx;
}

.feedback-text {
  flex: 1;
  padding: 0 18rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #484848;
  line-height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.feedback-arrow {
  width: 11rpx;
  height: 19rpx;
}

.logout-btn {
  margin: 30rpx;
  background-color: #ff3b30;
  color: #fff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
}

/* 动画效果 */
.animation-fade {
  animation: fadeIn 0.6s ease-out;
}

.animation-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animation-slide-right {
  animation: slideInRight 0.6s ease-out;
}

.animation-slide-bottom {
  animation: slideInBottom 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 核销码弹窗样式 */
.verify-popup-content {
  width: 640rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin: 20rpx;
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .verify-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.verify-main {
  padding: 32rpx;
  text-align: center;
}

.qr-section {
  margin-bottom: 32rpx;

  .qr-container {
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto;
    background-color: #fff;
    border: 2rpx solid #f0f0f0;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    position: relative;
    z-index: 1;

    .qr-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .placeholder-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .placeholder-text {
          font-size: 22rpx;
          color: #999;
          margin-top: 12rpx;
        }
      }
    }
  }
}

.code-info {
  margin-bottom: 32rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  position: relative;
  z-index: 2;

  .code-title {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    text-align: center;
  }

  .code-value {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    letter-spacing: 3rpx;
    font-family: 'Courier New', 'Monaco', monospace;
    margin-bottom: 16rpx;
    word-break: break-all;
    text-align: center;
    line-height: 1.4;
  }

  .code-actions {
    text-align: center;

    .action-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #fff;
      border-radius: 16rpx;
      font-size: 22rpx;
      color: #007aff;
      border: 1rpx solid #e0e0e0;

      text {
        margin-left: 6rpx;
      }
    }
  }
}

.tips {
  .tip-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.verify-footer {
  padding: 0 32rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 24rpx;
  padding-top: 24rpx;

  .u-button {
    width: 100%;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 600;
  }
}
</style>














