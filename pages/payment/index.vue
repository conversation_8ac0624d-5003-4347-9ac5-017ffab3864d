<template>
  <view class="payment-page">
    <!-- 导航栏 -->
    <u-navbar
        title="订单支付"
        :border-bottom="true"
        left-icon="arrow-left"
        @left-click="goBack"
    ></u-navbar>

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-header">
        <text class="order-title">订单信息</text>
      </view>
      <view class="order-detail">
        <view class="order-item">
          <text class="label">订单号：</text>
          <text class="value">{{ orderNo }}</text>
        </view>
        <view class="order-item">
          <text class="label">订单类型：</text>
          <text class="value">{{ orderTypeText }}</text>
        </view>
        <view class="order-item">
          <text class="label">支付金额：</text>
          <text class="value price">¥{{ payPrice }}</text>
        </view>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <view class="payment-header">
        <text class="payment-title">选择支付方式</text>
      </view>

      <!-- 微信支付 -->
      <view
          class="payment-item"
          :class="{ active: selectedPayType === 'weixin' }"
          @click="selectPayType('weixin')"
      >
        <view class="payment-left">
          <image class="payment-icon" src="/static/images/payment/wechat.png" mode="aspectFit"></image>
          <text class="payment-name">微信支付</text>
        </view>
        <view class="payment-right">
          <u-icon
              name="checkmark-circle-fill"
              color="#07c160"
              size="20"
              v-if="selectedPayType === 'weixin'"
          ></u-icon>
          <u-icon
              name="radio-button-off"
              color="#c8c9cc"
              size="20"
              v-else
          ></u-icon>
        </view>
      </view>

      <!-- 支付宝支付 -->
      <view
          class="payment-item"
          :class="{ active: selectedPayType === 'alipay' }"
          @click="selectPayType('alipay')"
      >
        <view class="payment-left">
          <image class="payment-icon" src="/static/images/payment/alipay.png" mode="aspectFit"></image>
          <text class="payment-name">支付宝支付</text>
        </view>
        <view class="payment-right">
          <u-icon
              name="checkmark-circle-fill"
              color="#1677ff"
              size="20"
              v-if="selectedPayType === 'alipay'"
          ></u-icon>
          <u-icon
              name="radio-button-off"
              color="#c8c9cc"
              size="20"
              v-else
          ></u-icon>
        </view>
      </view>

      <!-- 余额支付 -->
      <view
          class="payment-item"
          :class="{ active: selectedPayType === 'yue' }"
          @click="selectPayType('yue')"
      >
        <view class="payment-left">
          <image class="payment-icon" src="/static/images/payment/balance.png" mode="aspectFit"></image>
          <text class="payment-name">余额支付</text>
          <text class="balance-info">（余额：¥{{ userBalance }}）</text>
        </view>
        <view class="payment-right">
          <u-icon
              name="checkmark-circle-fill"
              color="#ff6b35"
              size="20"
              v-if="selectedPayType === 'yue'"
          ></u-icon>
          <u-icon
              name="radio-button-off"
              color="#c8c9cc"
              size="20"
              v-else
          ></u-icon>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-footer">
      <view class="payment-amount">
        <text class="amount-label">支付金额：</text>
        <text class="amount-value">¥{{ payPrice }}</text>
      </view>
      <u-button
          type="primary"
          :loading="paying"
          @click="confirmPayment"
          class="pay-button"
      >
        {{ paying ? '支付中...' : '确认支付' }}
      </u-button>
    </view>
  </view>
</template>

<script>
import {getPayConfig, orderPayment} from '@/nxTemp/apis/hotel'

export default {
  name: 'PaymentPage',
  data() {
    return {
      orderNo: '',
      payPrice: '0.00',
      orderType: '',
      selectedPayType: 'weixin', // 默认选择微信支付
      paying: false,
      userBalance: '0.00',
      payConfig: null
    }
  },

  computed: {
    orderTypeText() {
      const typeMap = {
        'hotel': '酒店预订',
        'order': '商品订单',
        'recharge': '余额充值'
      }
      return typeMap[this.orderType] || '订单支付'
    }
  },

  onLoad(options) {
    console.log('💰 支付页面参数:', options)

    this.orderNo = options.orderNo || ''
    this.payPrice = options.payPrice || '0.00'
    this.orderType = options.orderType || 'order'

    // 加载支付配置和用户余额
    this.loadPaymentData()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 选择支付方式
    selectPayType(payType) {
      this.selectedPayType = payType
      console.log('💳 选择支付方式:', payType)
    },

    // 加载支付相关数据
    async loadPaymentData() {
      try {
        // 获取支付配置
        const configRes = await getPayConfig()
        console.log('⚙️ 支付配置:', configRes)

        if (configRes.data && configRes.data.code === 200) {
          this.payConfig = configRes.data.data
        }

        // TODO: 获取用户余额
        // const balanceRes = await getUserBalance()
        // this.userBalance = balanceRes.data.data.balance || '0.00'

      } catch (error) {
        console.error('❌ 加载支付数据失败:', error)
      }
    },

    // 确认支付
    async confirmPayment() {
      if (!this.orderNo) {
        uni.$u.toast('订单号不能为空')
        return
      }

      if (!this.selectedPayType) {
        uni.$u.toast('请选择支付方式')
        return
      }

      this.paying = true

      try {
        console.log('🚀 开始支付流程')

        // 构建支付请求
        const paymentData = {
          orderNo: this.orderNo,
          payType: this.selectedPayType,
          payChannel: this.getPayChannel()
        }

        console.log('📤 支付请求数据:', paymentData)

        // 调用支付接口
        const paymentRes = await orderPayment(paymentData)
        console.log('✅ 支付响应:', paymentRes)

        if (paymentRes.data && paymentRes.data.code === 200) {
          const paymentResult = paymentRes.data.data

          // 根据支付方式处理结果
          await this.handlePaymentResult(paymentResult)
        } else {
          throw new Error(paymentRes.data?.message || '支付失败')
        }

      } catch (error) {
        console.error('❌ 支付失败:', error)
        uni.$u.toast(error.message || '支付失败，请重试')
      } finally {
        this.paying = false
      }
    },

    // 获取支付渠道
    getPayChannel() {
      const channelMap = {
        'weixin': 'mini', // 小程序支付
        'alipay': 'alipay',
        'yue': 'yue'
      }
      return channelMap[this.selectedPayType] || 'mini'
    },

    // 处理支付结果
    async handlePaymentResult(paymentResult) {
      console.log('🎯 处理支付结果:', paymentResult)

      if (this.selectedPayType === 'yue') {
        // 余额支付直接成功
        if (paymentResult.status) {
          this.showPaymentSuccess()
        } else {
          throw new Error('余额支付失败')
        }
      } else if (this.selectedPayType === 'weixin') {
        // 微信支付
        if (paymentResult.jsConfig) {
          await this.wechatPay(paymentResult.jsConfig)
        } else {
          throw new Error('微信支付配置错误')
        }
      } else if (this.selectedPayType === 'alipay') {
        // 支付宝支付
        if (paymentResult.alipayRequest) {
          await this.alipayPay(paymentResult.alipayRequest)
        } else {
          throw new Error('支付宝支付配置错误')
        }
      }
    },

    // 微信支付
    async wechatPay(jsConfig) {
      console.log('💰 调起微信支付:', jsConfig)

      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: jsConfig.timeStamp,
        nonceStr: jsConfig.nonceStr,
        package: jsConfig.packages,
        signType: jsConfig.signType,
        paySign: jsConfig.paySign,
        success: (res) => {
          console.log('✅ 微信支付成功:', res)
          this.showPaymentSuccess()
        },
        fail: (err) => {
          console.error('❌ 微信支付失败:', err)
          if (err.errMsg.includes('cancel')) {
            uni.$u.toast('支付已取消')
          } else {
            uni.$u.toast('支付失败，请重试')
          }
        }
      })
    },

    // 支付宝支付
    async alipayPay(alipayRequest) {
      console.log('💰 调起支付宝支付:', alipayRequest)

      uni.requestPayment({
        provider: 'alipay',
        orderInfo: alipayRequest,
        success: (res) => {
          console.log('✅ 支付宝支付成功:', res)
          this.showPaymentSuccess()
        },
        fail: (err) => {
          console.error('❌ 支付宝支付失败:', err)
          if (err.errMsg.includes('cancel')) {
            uni.$u.toast('支付已取消')
          } else {
            uni.$u.toast('支付失败，请重试')
          }
        }
      })
    },

    // 显示支付成功
    showPaymentSuccess() {
      uni.showModal({
        title: '支付成功',
        content: `订单支付成功！\n订单号：${this.orderNo}\n\n您可以在订单列表中查看订单状态。`,
        showCancel: false,
        confirmText: '知道了',
        success: () => {
          // 返回到首页或订单列表
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.order-info {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .order-header {
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .order-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .order-detail {
    padding: 30rpx;

    .order-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666;
      }

      .value {
        font-size: 28rpx;
        color: #333;

        &.price {
          color: #ff6b35;
          font-weight: 600;
          font-size: 32rpx;
        }
      }
    }
  }
}

.payment-methods {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .payment-header {
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .payment-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      background-color: #f8f9fa;
    }

    .payment-left {
      display: flex;
      align-items: center;

      .payment-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 20rpx;
      }

      .payment-name {
        font-size: 30rpx;
        color: #333;
        margin-right: 10rpx;
      }

      .balance-info {
        font-size: 24rpx;
        color: #999;
      }
    }

    .payment-right {
      display: flex;
      align-items: center;
    }
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .payment-amount {
    display: flex;
    align-items: center;

    .amount-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 10rpx;
    }

    .amount-value {
      font-size: 36rpx;
      font-weight: 600;
      color: #ff6b35;
    }
  }

  .pay-button {
    width: 240rpx;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
  }
}
</style>
