package com.zbkj.service.service.hotel.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.enums.DateTypeEnum;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.admin.SystemAdmin;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.model.hotel.HotelRoomPriceStrategy;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelPriceCalendarRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategyRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategySearchRequest;
import com.zbkj.common.response.hotel.HotelPriceCalendarResponse;
import com.zbkj.common.response.hotel.HotelPriceStrategyResponse;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.dao.hotel.HotelRoomPriceStrategyDao;
import com.zbkj.service.service.ChineseCalendarService;
import com.zbkj.service.service.hotel.HotelProductSyncService;
import com.zbkj.service.service.hotel.HotelRoomPriceStrategyService;
import com.zbkj.service.service.hotel.HotelRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒店房间价格策略服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelRoomPriceStrategyServiceImpl extends ServiceImpl<HotelRoomPriceStrategyDao, HotelRoomPriceStrategy> implements HotelRoomPriceStrategyService {

    @Autowired
    private HotelRoomService hotelRoomService;

    @Autowired
    private HotelProductSyncService productSyncService;

    @Autowired
    private ChineseCalendarService chineseCalendarService;

    @Override
    public List<HotelRoomPriceStrategy> getByRoomId(Integer roomId) {
        if (roomId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getStatus, 1) // 启用状态
               .eq(HotelRoomPriceStrategy::getIsDel, 0) // 未删除
               .orderByDesc(HotelRoomPriceStrategy::getPriority) // 按优先级倒序
               .orderByAsc(HotelRoomPriceStrategy::getStrategyType); // 策略类型正序

        return list(wrapper);
    }

    @Override
    public HotelRoomPriceStrategy getByRoomIdAndType(Integer roomId, Integer strategyType) {
        if (roomId == null || strategyType == null) {
            return null;
        }

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getStrategyType, strategyType)
               .eq(HotelRoomPriceStrategy::getStatus, 1)
               .eq(HotelRoomPriceStrategy::getIsDel, 0)
               .orderByDesc(HotelRoomPriceStrategy::getPriority)
               .last("LIMIT 1");

        return getOne(wrapper);
    }

    @Override
    public List<HotelRoomPriceStrategy> getByMerchantId(Integer merchantId) {
        if (merchantId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getMerId, merchantId)
               .eq(HotelRoomPriceStrategy::getStatus, 1)
               .eq(HotelRoomPriceStrategy::getIsDel, 0)
               .orderByAsc(HotelRoomPriceStrategy::getRoomId)
               .orderByDesc(HotelRoomPriceStrategy::getPriority);

        return list(wrapper);
    }

    @Override
    public boolean hasStrategyByRoomId(Integer roomId) {
        if (roomId == null) {
            return false;
        }

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getStatus, 1)
               .eq(HotelRoomPriceStrategy::getIsDel, 0);

        return count(wrapper) > 0;
    }

    @Override
    public boolean updateStrategyStatus(Integer strategyId, Integer status) {
        if (strategyId == null || status == null) {
            return false;
        }

        HotelRoomPriceStrategy strategy = new HotelRoomPriceStrategy();
        strategy.setId(strategyId);
        strategy.setStatus(status);

        return updateById(strategy);
    }

    @Override
    public long countByRoomId(Integer roomId) {
        if (roomId == null) {
            return 0;
        }

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getIsDel, 0);

        return count(wrapper);
    }

    @Override
    public PageInfo<HotelPriceStrategyResponse> getMerchantPage(HotelPriceStrategySearchRequest request, PageParamRequest pageParamRequest) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        Page<HotelRoomPriceStrategy> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getMerId, merchantId)
               .eq(HotelRoomPriceStrategy::getIsDel, 0);

        // 搜索条件
        if (request.getRoomId() != null) {
            wrapper.eq(HotelRoomPriceStrategy::getRoomId, request.getRoomId());
        }
        if (StrUtil.isNotBlank(request.getStrategyName())) {
            wrapper.like(HotelRoomPriceStrategy::getStrategyName, request.getStrategyName());
        }
        if (request.getStrategyType() != null) {
            wrapper.eq(HotelRoomPriceStrategy::getStrategyType, request.getStrategyType());
        }
        if (request.getStatus() != null) {
            wrapper.eq(HotelRoomPriceStrategy::getStatus, request.getStatus());
        }

        // 排序
        wrapper.orderByAsc(HotelRoomPriceStrategy::getRoomId)
               .orderByDesc(HotelRoomPriceStrategy::getPriority)
               .orderByDesc(HotelRoomPriceStrategy::getCreateTime);

        List<HotelRoomPriceStrategy> list = list(wrapper);

        // 转换为响应对象
        List<HotelPriceStrategyResponse> responseList = list.stream().map(this::convertToResponse).collect(Collectors.toList());

        return CommonPage.copyPageInfo(page, responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "hotel:room:details", allEntries = true),
            @CacheEvict(value = "hotel:room:list", allEntries = true),
            @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean savePriceStrategy(HotelPriceStrategyRequest request) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 验证房间是否属于当前商户
        HotelRoom room = hotelRoomService.getById(request.getRoomId());
        if (room == null || room.getIsDel() == 1 || !room.getMerId().equals(merchantId)) {
            throw new CrmebException("房间不存在或无权限操作");
        }

        // 检查策略名称是否重复
        if (checkStrategyNameExists(request.getStrategyName(), request.getRoomId(), null)) {
            throw new CrmebException("策略名称已存在");
        }

        // 检查策略类型是否重复（基础策略类型1-3不能重复）
        if (request.getStrategyType() <= 3 && checkStrategyTypeExists(request.getStrategyType(), request.getRoomId(), null)) {
            throw new CrmebException("该房间已存在相同类型的价格策略");
        }

        // 验证日期范围策略的参数
        if (request.getStrategyType() == 4) {
            if (request.getStartDate() == null || request.getEndDate() == null) {
                throw new CrmebException("日期范围策略必须设置开始日期和结束日期");
            }
            if (request.getStartDate().after(request.getEndDate())) {
                throw new CrmebException("开始日期不能晚于结束日期");
            }
        }

        // 验证具体日期策略的参数
        if (request.getStrategyType() == 5) {
            if (StrUtil.isBlank(request.getSpecificDates())) {
                throw new CrmebException("具体日期策略必须设置具体日期");
            }
        }

        HotelRoomPriceStrategy strategy = new HotelRoomPriceStrategy();
        BeanUtil.copyProperties(request, strategy);
        strategy.setMerId(merchantId);
        strategy.setIsDel(0);
        strategy.setCreateId(admin.getId());
        strategy.setCreateTime(new Date());
        strategy.setUpdateTime(new Date());

        // 设置默认值
        if (strategy.getStatus() == null) {
            strategy.setStatus(1);
        }

        boolean result = super.save(strategy);

        // 保存成功后，立即清除相关缓存并触发商品同步
        if (result) {
            try {
                // 立即清除相关缓存
                log.info("价格策略保存成功，开始清除缓存和同步商品，房间ID: {}", request.getRoomId());

                // 触发商品价格更新（这会清除商品相关缓存）
                productSyncService.updateHotelProductPrices(request.getRoomId());

                log.info("价格策略保存完成，缓存已清除，商品价格已更新，房间ID: {}", request.getRoomId());
            } catch (Exception e) {
                log.error("价格策略保存后同步商品失败，房间ID: {}", request.getRoomId(), e);
                // 同步失败时记录错误，缓存已通过@CacheEvict注解清除
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "hotel:room:details", allEntries = true),
            @CacheEvict(value = "hotel:room:list", allEntries = true),
            @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean updatePriceStrategy(HotelPriceStrategyRequest request) {
        if (request.getId() == null) {
            throw new CrmebException("策略ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查策略是否存在且属于当前商户
        HotelRoomPriceStrategy existStrategy = getById(request.getId());
        if (existStrategy == null || existStrategy.getIsDel() == 1) {
            throw new CrmebException("价格策略不存在");
        }
        if (!existStrategy.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限操作此价格策略");
        }

        // 验证房间是否属于当前商户
        HotelRoom room = hotelRoomService.getById(request.getRoomId());
        if (room == null || room.getIsDel() == 1 || !room.getMerId().equals(merchantId)) {
            throw new CrmebException("房间不存在或无权限操作");
        }

        // 检查策略名称是否重复
        if (checkStrategyNameExists(request.getStrategyName(), request.getRoomId(), request.getId())) {
            throw new CrmebException("策略名称已存在");
        }

        // 检查策略类型是否重复（基础策略类型1-3不能重复）
        if (request.getStrategyType() <= 3 && checkStrategyTypeExists(request.getStrategyType(), request.getRoomId(), request.getId())) {
            throw new CrmebException("该房间已存在相同类型的价格策略");
        }

        // 验证日期范围策略的参数
        if (request.getStrategyType() == 4) {
            if (request.getStartDate() == null || request.getEndDate() == null) {
                throw new CrmebException("日期范围策略必须设置开始日期和结束日期");
            }
            if (request.getStartDate().after(request.getEndDate())) {
                throw new CrmebException("开始日期不能晚于结束日期");
            }
        }

        // 验证具体日期策略的参数
        if (request.getStrategyType() == 5) {
            if (StrUtil.isBlank(request.getSpecificDates())) {
                throw new CrmebException("具体日期策略必须设置具体日期");
            }
        }

        HotelRoomPriceStrategy strategy = new HotelRoomPriceStrategy();
        BeanUtil.copyProperties(request, strategy);
        strategy.setMerId(merchantId);
        strategy.setUpdateTime(new Date());

        boolean result = updateById(strategy);

        // 更新成功后，立即清除相关缓存并触发商品同步
        if (result) {
            try {
                // 立即清除相关缓存
                log.info("价格策略更新成功，开始清除缓存和同步商品，房间ID: {}", request.getRoomId());

                // 触发商品价格更新（这会清除商品相关缓存）
                productSyncService.updateHotelProductPrices(request.getRoomId());

                log.info("价格策略更新完成，缓存已清除，商品价格已更新，房间ID: {}", request.getRoomId());
            } catch (Exception e) {
                log.error("价格策略更新后同步商品失败，房间ID: {}", request.getRoomId(), e);
                // 同步失败时记录错误，缓存已通过@CacheEvict注解清除
            }
        }

        return result;
    }

    @Override
    public HotelPriceStrategyResponse getStrategyInfo(Integer id) {
        if (id == null) {
            throw new CrmebException("策略ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelRoomPriceStrategy strategy = getById(id);
        if (strategy == null || strategy.getIsDel() == 1) {
            throw new CrmebException("价格策略不存在");
        }
        if (!strategy.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限查看此价格策略");
        }

        return convertToResponse(strategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "hotel:room:details", allEntries = true),
            @CacheEvict(value = "hotel:room:list", allEntries = true),
            @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean deleteStrategy(Integer id) {
        if (id == null) {
            throw new CrmebException("策略ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelRoomPriceStrategy strategy = getById(id);
        if (strategy == null || strategy.getIsDel() == 1) {
            throw new CrmebException("价格策略不存在");
        }
        if (!strategy.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限删除此价格策略");
        }

        // 软删除 - 使用LambdaUpdateWrapper避免MyBatis-Plus逻辑删除冲突
        LambdaUpdateWrapper<HotelRoomPriceStrategy> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(HotelRoomPriceStrategy::getId, id)
                     .set(HotelRoomPriceStrategy::getIsDel, 1)
                     .set(HotelRoomPriceStrategy::getUpdateTime, new Date());

        boolean result = update(updateWrapper);

        // 删除成功后，触发商品同步
        if (result) {
            try {
                productSyncService.syncHotelProductsByRoom(strategy.getRoomId());
            } catch (Exception e) {
                log.warn("价格策略删除后同步商品失败，房间ID: {}", strategy.getRoomId(), e);
            }
        }

        return result;
    }

    @Override
    public boolean checkStrategyNameExists(String strategyName, Integer roomId, Integer excludeId) {
        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getStrategyName, strategyName)
               .eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getIsDel, 0);

        if (excludeId != null) {
            wrapper.ne(HotelRoomPriceStrategy::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    @Override
    public boolean checkStrategyTypeExists(Integer strategyType, Integer roomId, Integer excludeId) {
        LambdaQueryWrapper<HotelRoomPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoomPriceStrategy::getStrategyType, strategyType)
               .eq(HotelRoomPriceStrategy::getRoomId, roomId)
               .eq(HotelRoomPriceStrategy::getIsDel, 0);

        if (excludeId != null) {
            wrapper.ne(HotelRoomPriceStrategy::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    /**
     * 转换为响应对象
     */
    private HotelPriceStrategyResponse convertToResponse(HotelRoomPriceStrategy strategy) {
        HotelPriceStrategyResponse response = new HotelPriceStrategyResponse();
        BeanUtil.copyProperties(strategy, response);

        // 设置状态描述
        response.setStatusDesc(strategy.getStatus() == 1 ? "启用" : "禁用");

        // 设置策略类型描述
        response.setStrategyTypeDesc(getStrategyTypeDesc(strategy.getStrategyType()));

        // 获取房型名称
        try {
            HotelRoom room = hotelRoomService.getById(strategy.getRoomId());
            if (room != null) {
                response.setRoomName(room.getRoomName());
            }
        } catch (Exception e) {
            log.warn("获取房型名称失败，房型ID: {}", strategy.getRoomId(), e);
        }

        return response;
    }

    /**
     * 获取策略类型描述
     */
    private String getStrategyTypeDesc(Integer strategyType) {
        switch (strategyType) {
            case 1:
                return "基础价格(工作日)";
            case 2:
                return "周末价格";
            case 3:
                return "节假日价格";
            case 4:
                return "按日期范围";
            case 5:
                return "按具体日期";
            default:
                return "未知类型";
        }
    }

    @Override
    public HotelPriceCalendarResponse getPriceCalendar(HotelPriceCalendarRequest request) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 验证房间是否属于当前商户
        HotelRoom room = hotelRoomService.getById(request.getRoomId());
        if (room == null || room.getIsDel() == 1 || !room.getMerId().equals(merchantId)) {
            throw new CrmebException("房间不存在或无权限操作");
        }

        // 获取该房间的所有价格策略
        List<HotelRoomPriceStrategy> strategies = getByRoomId(request.getRoomId());

        // 构建响应对象
        HotelPriceCalendarResponse response = new HotelPriceCalendarResponse();
        response.setRoomId(request.getRoomId());
        response.setRoomName(room.getRoomName());
        response.setYear(request.getYear());
        response.setMonth(request.getMonth());

        // 生成该月的所有日期价格
        List<HotelPriceCalendarResponse.DayPrice> dayPrices = new ArrayList<>();
        YearMonth yearMonth = YearMonth.of(request.getYear(), request.getMonth());
        int daysInMonth = yearMonth.lengthOfMonth();

        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = LocalDate.of(request.getYear(), request.getMonth(), day);
            HotelPriceCalendarResponse.DayPrice dayPrice = calculateDayPrice(date, strategies);
            dayPrices.add(dayPrice);
        }

        response.setDayPrices(dayPrices);
        return response;
    }

    /**
     * 计算单日价格
     *
     * 根据给定日期和一系列价格策略，计算并返回该日期的酒店房间价格信息
     * 此方法首先确定日期的类型（如工作日、周末等），然后根据日期类型和预定义的价格策略
     * 来确定适用于该日期的价格策略最后，将价格信息封装到DayPrice对象中并返回
     *
     * @param date 日期，用于计算价格的特定日期
     * @param strategies 价格策略列表，包含不同日期类型的价格策略
     * @return 返回封装了单日价格信息的DayPrice对象
     */
    private HotelPriceCalendarResponse.DayPrice calculateDayPrice(LocalDate date, List<HotelRoomPriceStrategy> strategies) {
        // 初始化DayPrice对象以存储计算结果
        HotelPriceCalendarResponse.DayPrice dayPrice = new HotelPriceCalendarResponse.DayPrice();
        dayPrice.setDay(date.getDayOfMonth());
        dayPrice.setDate(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        try {
            // 获取日期类型
            DateTypeEnum dateType = chineseCalendarService.getDateType(date);
            dayPrice.setDateType(dateType.name());
            dayPrice.setDateTypeDesc(getDateTypeDesc(dateType));

            // 匹配价格策略
            HotelRoomPriceStrategy matchedStrategy = strategies.stream()
                    .filter(s -> matchesDateType(s, dateType, date))
                    .max((s1, s2) -> s1.getPriority().compareTo(s2.getPriority()))
                    .orElse(null);

            if (matchedStrategy != null) {
                dayPrice.setPrice(matchedStrategy.getPriceValue());
                dayPrice.setStrategyName(matchedStrategy.getStrategyName());
                dayPrice.setHasStrategy(true);
            } else {
                dayPrice.setPrice(BigDecimal.ZERO);
                dayPrice.setStrategyName("无策略");
                dayPrice.setHasStrategy(false);
            }

        } catch (Exception e) {
            // 处理计算日期价格时可能发生的异常
            log.warn("计算日期价格失败: {}", date, e);
            dayPrice.setPrice(BigDecimal.ZERO);
            dayPrice.setStrategyName("计算失败");
            dayPrice.setHasStrategy(false);
            dayPrice.setDateType("UNKNOWN");
            dayPrice.setDateTypeDesc("未知");
        }

        // 返回计算后的单日价格信息
        return dayPrice;
    }


    /**
     * 获取日期类型描述
     */
    private String getDateTypeDesc(DateTypeEnum dateType) {
        switch (dateType) {
            case WORKDAY:
                return "工作日";
            case WEEKEND:
                return "周末";
            case HOLIDAY:
                return "节假日";
            case TRANSFER_WORKDAY:
                return "调休工作日";
            default:
                return "未知";
        }
    }

    /**
     * 判断价格策略是否匹配日期类型（复用已有逻辑）
     */
    private boolean matchesDateType(HotelRoomPriceStrategy strategy, DateTypeEnum dateType, LocalDate targetDate) {
        switch (strategy.getStrategyType()) {
            case 1: // 基础价格(工作日)
                return dateType == DateTypeEnum.WORKDAY || dateType == DateTypeEnum.TRANSFER_WORKDAY;
            case 2: // 周末价格
                return dateType == DateTypeEnum.WEEKEND;
            case 3: // 节假日价格
                return dateType == DateTypeEnum.HOLIDAY;
            case 4: // 按日期范围
                return matchesDateRange(strategy, targetDate);
            case 5: // 按具体日期
                return matchesSpecificDates(strategy, targetDate);
            default:
                return false;
        }
    }

    /**
     * 匹配日期范围
     */
    private boolean matchesDateRange(HotelRoomPriceStrategy strategy, LocalDate targetDate) {
        if (strategy.getStartDate() == null || strategy.getEndDate() == null || targetDate == null) {
            return false;
        }

        LocalDate startDate = dateToLocalDate(strategy.getStartDate());
        LocalDate endDate = dateToLocalDate(strategy.getEndDate());

        return !targetDate.isBefore(startDate) && !targetDate.isAfter(endDate);
    }

    /**
     * 匹配具体日期
     */
    private boolean matchesSpecificDates(HotelRoomPriceStrategy strategy, LocalDate targetDate) {
        if (StrUtil.isBlank(strategy.getSpecificDates()) || targetDate == null) {
            return false;
        }

        try {
            List<String> specificDates = JSON.parseArray(strategy.getSpecificDates(), String.class);
            String targetDateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return specificDates.contains(targetDateStr);
        } catch (Exception e) {
            log.warn("解析具体日期失败: {}", strategy.getSpecificDates(), e);
            return false;
        }
    }

    /**
     * Date转LocalDate
     */
    private LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 前端获取价格日历（无需商户权限验证）
     */
    @Override
    public HotelPriceCalendarResponse getFrontPriceCalendar(HotelPriceCalendarRequest request) {
        log.info("前端查询价格日历 - 房型ID: {}, 年月: {}-{}",
                request.getRoomId(), request.getYear(), request.getMonth());

        // 验证房型是否存在
        HotelRoom room = hotelRoomService.getById(request.getRoomId());
        if (room == null) {
            throw new CrmebException("房型不存在");
        }

        // 直接调用现有的价格日历方法，但不进行商户权限验证
        return buildPriceCalendar(request);
    }

    /**
     * 前端获取指定日期的价格
     */
    @Override
    public HotelPriceCalendarResponse.DayPrice getFrontDayPrice(Integer roomId, String date) {
        log.info("前端查询单日价格 - 房型ID: {}, 日期: {}", roomId, date);

        // 验证房型是否存在
        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            throw new CrmebException("房型不存在");
        }

        try {
            LocalDate targetDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 获取房型的价格策略
            List<HotelRoomPriceStrategy> strategies = getByRoomId(roomId);

            HotelPriceCalendarResponse.DayPrice dayPrice = new HotelPriceCalendarResponse.DayPrice();
            dayPrice.setDay(targetDate.getDayOfMonth());
            dayPrice.setDate(date);

            // 计算价格
            DateTypeEnum dateType = chineseCalendarService.getDateType(targetDate);
            HotelRoomPriceStrategy matchedStrategy = strategies.stream()
                    .filter(s -> matchesDateType(s, dateType, targetDate))
                    .max((s1, s2) -> s1.getPriority().compareTo(s2.getPriority()))
                    .orElse(null);

            if (matchedStrategy != null) {
                dayPrice.setPrice(matchedStrategy.getPriceValue());
                dayPrice.setStrategyName(matchedStrategy.getStrategyName());
                dayPrice.setHasStrategy(true);
            } else {
                dayPrice.setPrice(BigDecimal.ZERO);
                dayPrice.setStrategyName("无策略");
                dayPrice.setHasStrategy(false);
            }

            dayPrice.setDateType(dateType.name());
            dayPrice.setDateTypeDesc(getDateTypeDesc(dateType));

            return dayPrice;
        } catch (Exception e) {
            log.error("解析日期失败: {}", date, e);
            throw new CrmebException("日期格式错误");
        }
    }

    /**
     * 前端批量获取多个日期的价格
     */
    @Override
    public List<HotelPriceCalendarResponse.DayPrice> getFrontBatchDayPrices(Integer roomId, List<String> dates) {
        log.info("前端批量查询价格 - 房型ID: {}, 日期数量: {}", roomId, dates.size());

        // 验证房型是否存在
        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            throw new CrmebException("房型不存在");
        }

        // 获取房型的价格策略
        List<HotelRoomPriceStrategy> strategies = getByRoomId(roomId);
        List<HotelPriceCalendarResponse.DayPrice> result = new ArrayList<>();

        for (String date : dates) {
            try {
                LocalDate targetDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                HotelPriceCalendarResponse.DayPrice dayPrice = new HotelPriceCalendarResponse.DayPrice();
                dayPrice.setDay(targetDate.getDayOfMonth());
                dayPrice.setDate(date);

                // 计算价格
                DateTypeEnum dateType = chineseCalendarService.getDateType(targetDate);
                HotelRoomPriceStrategy matchedStrategy = strategies.stream()
                        .filter(s -> matchesDateType(s, dateType, targetDate))
                        .max((s1, s2) -> s1.getPriority().compareTo(s2.getPriority()))
                        .orElse(null);

                if (matchedStrategy != null) {
                    dayPrice.setPrice(matchedStrategy.getPriceValue());
                    dayPrice.setStrategyName(matchedStrategy.getStrategyName());
                    dayPrice.setHasStrategy(true);
                } else {
                    dayPrice.setPrice(BigDecimal.ZERO);
                    dayPrice.setStrategyName("无策略");
                    dayPrice.setHasStrategy(false);
                }

                dayPrice.setDateType(dateType.name());
                dayPrice.setDateTypeDesc(getDateTypeDesc(dateType));

                result.add(dayPrice);
            } catch (Exception e) {
                log.warn("解析日期失败: {}", date, e);
                // 添加一个错误的价格记录
                HotelPriceCalendarResponse.DayPrice dayPrice = new HotelPriceCalendarResponse.DayPrice();
                dayPrice.setDate(date);
                dayPrice.setPrice(BigDecimal.ZERO);
                dayPrice.setHasStrategy(false);
                result.add(dayPrice);
            }
        }

        return result;
    }

    /**
     * 构建价格日历（提取公共逻辑）
     */
    private HotelPriceCalendarResponse buildPriceCalendar(HotelPriceCalendarRequest request) {
        YearMonth yearMonth = YearMonth.of(request.getYear(), request.getMonth());
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();

        // 获取房型信息
        HotelRoom room = hotelRoomService.getById(request.getRoomId());

        HotelPriceCalendarResponse response = new HotelPriceCalendarResponse();
        response.setRoomId(request.getRoomId());
        response.setRoomName(room != null ? room.getRoomName() : "");
        response.setYear(request.getYear());
        response.setMonth(request.getMonth());

        // 获取房型的价格策略
        List<HotelRoomPriceStrategy> strategies = getByRoomId(request.getRoomId());
        List<HotelPriceCalendarResponse.DayPrice> dayPrices = new ArrayList<>();

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            final LocalDate finalCurrentDate = currentDate; // 创建final变量供lambda使用

            HotelPriceCalendarResponse.DayPrice dayPrice = new HotelPriceCalendarResponse.DayPrice();
            dayPrice.setDay(finalCurrentDate.getDayOfMonth());
            dayPrice.setDate(finalCurrentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            // 计算价格
            DateTypeEnum dateType = chineseCalendarService.getDateType(finalCurrentDate);
            HotelRoomPriceStrategy matchedStrategy = strategies.stream()
                    .filter(s -> matchesDateType(s, dateType, finalCurrentDate))
                    .max((s1, s2) -> s1.getPriority().compareTo(s2.getPriority()))
                    .orElse(null);

            if (matchedStrategy != null) {
                dayPrice.setPrice(matchedStrategy.getPriceValue());
                dayPrice.setStrategyName(matchedStrategy.getStrategyName());
                dayPrice.setHasStrategy(true);
            } else {
                dayPrice.setPrice(BigDecimal.ZERO);
                dayPrice.setStrategyName("无策略");
                dayPrice.setHasStrategy(false);
            }

            dayPrice.setDateType(dateType.name());
            dayPrice.setDateTypeDesc(getDateTypeDesc(dateType));

            dayPrices.add(dayPrice);
            currentDate = currentDate.plusDays(1);
        }

        response.setDayPrices(dayPrices);
        return response;
    }
}
