package com.zbkj.service.service.hotel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.model.hotel.HotelRoomPriceStrategy;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelPriceCalendarRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategyRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategySearchRequest;
import com.zbkj.common.response.hotel.HotelPriceCalendarResponse;
import com.zbkj.common.response.hotel.HotelPriceStrategyResponse;

import java.util.List;

/**
 * 酒店房间价格策略服务接口
 * 
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelRoomPriceStrategyService extends IService<HotelRoomPriceStrategy> {

    /**
     * 根据房间ID获取价格策略列表
     * 
     * @param roomId 房间ID
     * @return 价格策略列表
     */
    List<HotelRoomPriceStrategy> getByRoomId(Integer roomId);

    /**
     * 根据房间ID和策略类型获取价格策略
     * 
     * @param roomId 房间ID
     * @param strategyType 策略类型
     * @return 价格策略
     */
    HotelRoomPriceStrategy getByRoomIdAndType(Integer roomId, Integer strategyType);

    /**
     * 获取指定商户的所有价格策略
     * 
     * @param merchantId 商户ID
     * @return 价格策略列表
     */
    List<HotelRoomPriceStrategy> getByMerchantId(Integer merchantId);

    /**
     * 检查房间是否有价格策略配置
     * 
     * @param roomId 房间ID
     * @return 是否有配置
     */
    boolean hasStrategyByRoomId(Integer roomId);

    /**
     * 更新价格策略状态
     *
     * @param strategyId 策略ID
     * @param status 状态：0-禁用，1-启用
     * @return 更新结果
     */
    boolean updateStrategyStatus(Integer strategyId, Integer status);

    /**
     * 统计房间的价格策略数量
     *
     * @param roomId 房间ID
     * @return 价格策略数量
     */
    long countByRoomId(Integer roomId);

    /**
     * 商户端分页查询价格策略列表
     *
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<HotelPriceStrategyResponse> getMerchantPage(HotelPriceStrategySearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 新增价格策略
     *
     * @param request 价格策略信息
     * @return 新增结果
     */
    boolean savePriceStrategy(HotelPriceStrategyRequest request);

    /**
     * 修改价格策略
     *
     * @param request 价格策略信息
     * @return 修改结果
     */
    boolean updatePriceStrategy(HotelPriceStrategyRequest request);

    /**
     * 获取价格策略详情
     *
     * @param id 策略ID
     * @return 策略详情
     */
    HotelPriceStrategyResponse getStrategyInfo(Integer id);

    /**
     * 删除价格策略
     *
     * @param id 策略ID
     * @return 删除结果
     */
    boolean deleteStrategy(Integer id);

    /**
     * 检查策略名称是否重复
     *
     * @param strategyName 策略名称
     * @param roomId 房间ID
     * @param excludeId 排除的策略ID(修改时使用)
     * @return 是否重复
     */
    boolean checkStrategyNameExists(String strategyName, Integer roomId, Integer excludeId);

    /**
     * 检查策略类型是否重复
     *
     * @param strategyType 策略类型
     * @param roomId 房间ID
     * @param excludeId 排除的策略ID(修改时使用)
     * @return 是否重复
     */
    boolean checkStrategyTypeExists(Integer strategyType, Integer roomId, Integer excludeId);

    /**
     * 获取价格日历
     *
     * @param request 日历查询请求
     * @return 价格日历
     */
    HotelPriceCalendarResponse getPriceCalendar(HotelPriceCalendarRequest request);

    /**
     * 前端获取价格日历（无需商户权限验证）
     *
     * @param request 日历查询请求
     * @return 价格日历
     */
    HotelPriceCalendarResponse getFrontPriceCalendar(HotelPriceCalendarRequest request);

    /**
     * 前端获取指定日期的价格
     *
     * @param roomId 房型ID
     * @param date   日期(yyyy-MM-dd)
     * @return 日期价格信息
     */
    HotelPriceCalendarResponse.DayPrice getFrontDayPrice(Integer roomId, String date);

    /**
     * 前端批量获取多个日期的价格
     *
     * @param roomId 房型ID
     * @param dates  日期列表(yyyy-MM-dd)
     * @return 日期价格列表
     */
    java.util.List<HotelPriceCalendarResponse.DayPrice> getFrontBatchDayPrices(Integer roomId, java.util.List<String> dates);
}
