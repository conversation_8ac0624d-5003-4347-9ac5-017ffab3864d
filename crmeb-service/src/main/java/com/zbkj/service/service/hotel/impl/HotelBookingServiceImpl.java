package com.zbkj.service.service.hotel.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.constants.HotelConstants;
import com.zbkj.common.enums.DateTypeEnum;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.model.merchant.Merchant;
import com.zbkj.common.model.product.Product;
import com.zbkj.common.model.product.ProductAttrValue;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomListRequest;
import com.zbkj.common.request.hotel.HotelSearchRequest;
import com.zbkj.common.response.hotel.HotelListResponse;
import com.zbkj.common.response.hotel.HotelRoomListResponse;
import com.zbkj.service.service.ChineseCalendarService;
import com.zbkj.service.service.MerchantService;
import com.zbkj.service.service.ProductAttrValueService;
import com.zbkj.service.service.ProductService;
import com.zbkj.service.service.hotel.HotelBookingService;
import com.zbkj.service.service.hotel.HotelRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 酒店预订服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelBookingServiceImpl implements HotelBookingService {

    @Autowired
    private ProductService productService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private HotelRoomService hotelRoomService;

    @Autowired
    private ChineseCalendarService chineseCalendarService;

    @Autowired
    private ProductAttrValueService productAttrValueService;

    @Override
    // 临时禁用酒店列表缓存，避免PageInfo序列化问题
    // @Cacheable(value = "hotel:list",
    //            key = "#request.keyword + ':' + #request.minPrice + ':' + #request.maxPrice + ':' + #request.sortType + ':' + #pageParamRequest.page + ':' + #pageParamRequest.limit",
    //            unless = "#result.list.isEmpty()")
    public PageInfo<HotelListResponse> getHotelList(HotelSearchRequest request, PageParamRequest pageParamRequest) {
        Page<HotelListResponse> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 查询酒店商品，按商户分组
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getType, HotelConstants.HOTEL_PRODUCT_TYPE)
               .eq(Product::getIsDel, false)
               .eq(Product::getIsShow, true);

        // 关键词搜索（酒店名称）
        if (StrUtil.isNotBlank(request.getKeyword())) {
            wrapper.like(Product::getName, request.getKeyword());
        }

        // 房型搜索（从商品名称中匹配房型）
        if (StrUtil.isNotBlank(request.getRoomType())) {
            wrapper.like(Product::getName, request.getRoomType());
        }

        // 价格筛选
        if (request.getMinPrice() != null) {
            wrapper.ge(Product::getPrice, request.getMinPrice());
        }
        if (request.getMaxPrice() != null) {
            wrapper.le(Product::getPrice, request.getMaxPrice());
        }

        // 查询商品列表
        List<Product> products = productService.list(wrapper);

        // 按商户分组
        Map<Integer, List<Product>> merchantProductMap = products.stream()
                .collect(Collectors.groupingBy(Product::getMerId));

        // 构建酒店列表响应
        List<HotelListResponse> hotelList = new ArrayList<>();
        for (Map.Entry<Integer, List<Product>> entry : merchantProductMap.entrySet()) {
            Integer merId = entry.getKey();
            List<Product> merchantProducts = entry.getValue();

            HotelListResponse hotel = buildHotelListResponse(merId, merchantProducts);
            if (hotel != null) {
                // 星级筛选
                if (request.getStarLevel() != null) {
                    if (hotel.getStarLevel() == null || !hotel.getStarLevel().equals(request.getStarLevel())) {
                        continue; // 跳过不符合星级要求的酒店
                    }
                }
                hotelList.add(hotel);
            }
        }

        // 排序处理
        sortHotelList(hotelList, request.getSortType());

        // 手动分页（因为我们需要先聚合再分页）
        int start = (pageParamRequest.getPage() - 1) * pageParamRequest.getLimit();
        int end = Math.min(start + pageParamRequest.getLimit(), hotelList.size());

        // 修复：使用ArrayList构造器避免SubList序列化问题
        List<HotelListResponse> pagedList = new ArrayList<>(hotelList.subList(start, end));

        // 构建分页信息
        PageInfo<HotelListResponse> pageInfo = new PageInfo<>(pagedList);
        pageInfo.setTotal(hotelList.size());
        pageInfo.setPages((int) Math.ceil((double) hotelList.size() / pageParamRequest.getLimit()));

        return pageInfo;
    }

    @Override
    public HotelListResponse getHotelDetail(Integer hotelId) {
        // 查询该商户的酒店商品
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getMerId, hotelId)
               .eq(Product::getType, HotelConstants.HOTEL_PRODUCT_TYPE)
               .eq(Product::getIsDel, false)
                .eq(Product::getIsShow, true);

        List<Product> products = productService.list(wrapper);
        if (CollUtil.isEmpty(products)) {
            throw new CrmebException("酒店不存在或暂无房型");
        }

        return buildHotelListResponse(hotelId, products);
    }

    @Override
    @Cacheable(value = "hotel:room:list",
               key = "#request.hotelId + ':' + #request.checkInDate + ':' + #request.checkOutDate",
            condition = "#request.checkInDate != null and #request.checkOutDate != null",
            unless = "#result.isEmpty()")
    public List<HotelRoomListResponse> getRoomList(HotelRoomListRequest request) {

        // 验证日期
        if (request.getCheckOutDate().before(request.getCheckInDate())) {
            throw new CrmebException("离店日期不能早于入住日期");
        }

        // 生成日期范围
        List<String> dateRange = generateDateRange(request.getCheckInDate(), request.getCheckOutDate());

        List<Product> products = optimizedQueryProductsByDates(request.getHotelId(), dateRange);

        if (CollUtil.isEmpty(products)) {
            return new ArrayList<>();
        }

        // 按房型聚合
        List<HotelRoomListResponse> result = aggregateRoomTypes(products, dateRange);

        return result;
    }

    @Override
    public HotelProductInfo parseProductName(String productName) {
        if (StrUtil.isBlank(productName)) {
            return null;
        }

        String[] parts = productName.split(HotelConstants.PRODUCT_NAME_SEPARATOR);

        if (parts.length < 3) {
            // 尝试使用正则表达式提取日期
            return parseProductNameWithRegex(productName);
        }

        // 处理日期部分：如果分割后有5个部分，说明日期被分割了，需要重新组合
        String dateStr;
        if (parts.length == 5) {
            // 格式：酒店名-房型名-2025-07-31 (被分割成5部分)
            dateStr = parts[2] + "-" + parts[3] + "-" + parts[4];
        } else if (parts.length == 3) {
            // 格式：酒店名-房型名-2025-07-31 (正常3部分)
            dateStr = parts[2];
        } else {
            return parseProductNameWithRegex(productName);
        }

        HotelProductInfo result = HotelProductInfo.builder()
                .hotelName(parts[0])
                .roomTypeName(parts[1])
                .checkInDate(dateStr)
                .build();

        return result;
    }

    /**
     * 构建酒店列表响应对象
     */
    private HotelListResponse buildHotelListResponse(Integer merId, List<Product> products) {
        try {
            Merchant merchant = getCachedMerchant(merId);

            HotelListResponse hotel = new HotelListResponse();
            hotel.setHotelId(merId);
            hotel.setHotelName(merchant.getName());
            hotel.setAddress(merchant.getAddressDetail());
            hotel.setIntro(merchant.getIntro());
            hotel.setStatus(merchant.getIsSwitch() ? 1 : 0);

            // 计算最低价格
            BigDecimal minPrice = products.stream()
                    .map(Product::getPrice)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            hotel.setMinPrice(minPrice);

            // 设置星级评分（从商户的star_level字段获取）
            hotel.setStarLevel(merchant.getStarLevel() != null ? merchant.getStarLevel() : 4);

            // 设置评分（模拟数据，后续可从评价表获取）
            hotel.setScore(new BigDecimal("4.8"));

            // 设置距离（模拟数据，后续可根据用户位置计算）
            hotel.setDistance(new BigDecimal("0"));

            // 设置房型数量
            hotel.setRoomTypeCount(products.size());

            // 设置酒店图片（从商户封面图和头像获取）
            List<String> hotelImages = new ArrayList<>();
            if (StrUtil.isNotBlank(merchant.getCoverImage())) {
                hotelImages.add(merchant.getCoverImage());
            }
            if (StrUtil.isNotBlank(merchant.getAvatar())) {
                hotelImages.add(merchant.getAvatar());
            }
            if (StrUtil.isNotBlank(merchant.getBackImage())) {
                hotelImages.add(merchant.getBackImage());
            }
            hotel.setHotelImages(hotelImages);

            // 设置主图
            hotel.setMainImage(StrUtil.isNotBlank(merchant.getCoverImage()) ?
                merchant.getCoverImage() : merchant.getAvatar());

            // 设置设施标签（从现有数据提取或模拟）
            List<String> facilities = buildHotelFacilities(merId);
            hotel.setFacilities(facilities);



            return hotel;

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建酒店设施标签
     */
    private List<String> buildHotelFacilities(Integer merId) {
        List<String> facilities = new ArrayList<>();

        try {
            // 从酒店房间表获取设施信息
            LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HotelRoom::getMerId, merId)
                   .eq(HotelRoom::getIsDel, false)
                   .eq(HotelRoom::getStatus, 1);

            List<HotelRoom> rooms = hotelRoomService.list(wrapper);

            Set<String> facilitySet = new HashSet<>();
            for (HotelRoom room : rooms) {
                if (StrUtil.isNotBlank(room.getRoomFacilities())) {
                    try {
                        // 解析JSON格式的设施列表
                        List<String> roomFacilities = JSONUtil.toList(JSONUtil.parseArray(room.getRoomFacilities()), String.class);
                        facilitySet.addAll(roomFacilities);
                    } catch (Exception e) {
                        // 如果JSON解析失败，尝试按逗号分割
                        String[] facilitiesInfo = room.getRoomFacilities().split(",");
                        for (String facility : facilitiesInfo) {
                            if (StrUtil.isNotBlank(facility.trim())) {
                                facilitySet.add(facility.trim());
                            }
                        }
                    }
                }
            }

            facilities.addAll(facilitySet);

            // 如果没有从房间获取到设施，提供默认设施
            if (facilities.isEmpty()) {
                facilities.addAll(Arrays.asList("免费WiFi", "停车场", "空调", "电视"));
            }

        } catch (Exception e) {
            // 提供默认设施
            facilities.addAll(Arrays.asList("免费WiFi", "停车场", "空调", "电视"));
        }

        return facilities;
    }

    /**
     * 酒店列表排序
     */
    private void sortHotelList(List<HotelListResponse> hotelList, Integer sortType) {
        if (sortType == null) {
            return;
        }

        switch (sortType) {
            case 1: // 价格升序
                hotelList.sort(Comparator.comparing(HotelListResponse::getMinPrice));
                break;
            case 2: // 价格降序
                hotelList.sort(Comparator.comparing(HotelListResponse::getMinPrice).reversed());
                break;
            case 3: // 距离（暂时按价格排序）
                hotelList.sort(Comparator.comparing(HotelListResponse::getDistance));
                break;
            case 4: // 评分
                hotelList.sort(Comparator.comparing(HotelListResponse::getScore).reversed());
                break;
            default:
                break;
        }
    }

    /**
     * 生成日期范围
     */
    private List<String> generateDateRange(Date startDate, Date endDate) {
        List<String> dateRange = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) {
            // 只生成入住日期，不包括离店日期
            if (calendar.getTime().before(endDate)) {
                dateRange.add(sdf.format(calendar.getTime()));
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return dateRange;
    }

    /**
     * 按房型聚合商品（性能优化：批量查询房型信息）
     */
    private List<HotelRoomListResponse> aggregateRoomTypes(List<Product> products, List<String> dateRange) {
        if (CollUtil.isEmpty(products)) {
            return new ArrayList<>();
        }

        // 按房型名称分组
        Map<String, List<Product>> roomTypeMap = products.stream()
                .collect(Collectors.groupingBy(product -> {
                    HotelProductInfo info = parseProductName(product.getName());
                    return info != null ? info.getRoomTypeName() : "未知房型";
                }));

        // 批量查询所有房型信息，避免N+1查询
        Integer merId = products.get(0).getMerId();
        Set<String> roomTypeNames = roomTypeMap.keySet();
        Map<String, HotelRoom> hotelRoomMap = batchQueryHotelRooms(merId, roomTypeNames);

        List<HotelRoomListResponse> roomList = new ArrayList<>();
        for (Map.Entry<String, List<Product>> entry : roomTypeMap.entrySet()) {
            String roomTypeName = entry.getKey();
            List<Product> roomProducts = entry.getValue();

            HotelRoomListResponse room = buildRoomListResponseOptimized(roomTypeName, roomProducts,
                    dateRange, hotelRoomMap.get(roomTypeName));

            // 过滤掉无效的房型数据（roomId为null表示数据库中没有对应的房型记录）
            if (room != null && room.getRoomId() != null) {
                roomList.add(room);
            }
        }

        return roomList;
    }

    /**
     * 构建房型列表响应对象（性能优化版本）
     */
    private HotelRoomListResponse buildRoomListResponseOptimized(String roomTypeName, List<Product> roomProducts,
            List<String> dateRange, HotelRoom hotelRoom) {
        if (CollUtil.isEmpty(roomProducts)) {
            return null;
        }

        HotelRoomListResponse room = new HotelRoomListResponse();
        Product firstProduct = roomProducts.get(0);

        if (hotelRoom != null) {
            // 使用eb_hotel_room表的房型信息
            room.setRoomId(hotelRoom.getId()); // 设置房型ID
            room.setRoomName(hotelRoom.getRoomName());
            room.setRoomType(hotelRoom.getRoomType());
            room.setRoomArea(hotelRoom.getRoomArea());
            room.setBedType(hotelRoom.getBedType());
            room.setMaxGuests(hotelRoom.getMaxGuests());
            room.setRoomDescription(hotelRoom.getRoomDescription());

            // 解析房间图片
            if (StrUtil.isNotBlank(hotelRoom.getRoomImages())) {
                try {
                    List<String> images = JSON.parseArray(hotelRoom.getRoomImages(), String.class);
                    room.setRoomImages(images);
                } catch (Exception e) {
                    room.setRoomImages(new ArrayList<>(Arrays.asList(firstProduct.getImage())));
                }
            } else {
                room.setRoomImages(new ArrayList<>(Arrays.asList(firstProduct.getImage())));
            }

            // 解析房间设施
            if (StrUtil.isNotBlank(hotelRoom.getRoomFacilities())) {
                try {
                    List<String> facilities = JSON.parseArray(hotelRoom.getRoomFacilities(), String.class);
                    room.setFacilities(facilities);
                } catch (Exception e) {
                    room.setFacilities(new ArrayList<>(Arrays.asList("免费WiFi", "空调", "电视", "热水")));
                }
            } else {
                room.setFacilities(new ArrayList<>(Arrays.asList("免费WiFi", "空调", "电视", "热水")));
            }
        } else {
            // 如果没有找到房型信息，使用默认值和商品信息
            room.setRoomId(null); // 没有找到房型信息时设置为null
            room.setRoomName(roomTypeName);
            room.setRoomType(roomTypeName);
            room.setRoomImages(new ArrayList<>(Arrays.asList(firstProduct.getImage())));
            room.setRoomDescription(firstProduct.getIntro());
            room.setRoomArea(new BigDecimal("25"));
            room.setBedType("大床");
            room.setMaxGuests(2);
            room.setFacilities(new ArrayList<>(Arrays.asList("免费WiFi", "空调", "电视", "热水")));
        }

        // 计算价格信息
        HotelRoomListResponse.PriceInfo priceInfo = calculatePriceInfo(roomProducts, dateRange);
        room.setPriceInfo(priceInfo);

        // 计算可用性信息
        HotelRoomListResponse.AvailabilityInfo availability = calculateAvailability(roomProducts, dateRange);
        room.setAvailability(availability);

        return room;
    }

    /**
     * 计算价格信息
     */
    private HotelRoomListResponse.PriceInfo calculatePriceInfo(List<Product> roomProducts, List<String> dateRange) {
        HotelRoomListResponse.PriceInfo priceInfo = new HotelRoomListResponse.PriceInfo();

        // 计算总价
        BigDecimal totalPrice = roomProducts.stream()
                .map(Product::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        priceInfo.setTotalPrice(totalPrice);
        priceInfo.setNights(dateRange.size());

        if (dateRange.size() > 0) {
            priceInfo.setAvgPrice(totalPrice.divide(new BigDecimal(dateRange.size()), 2, BigDecimal.ROUND_HALF_UP));
        }

        // 构建价格明细
        List<HotelRoomListResponse.PriceDetail> priceDetails = new ArrayList<>();
        for (Product product : roomProducts) {
            HotelProductInfo info = parseProductName(product.getName());
            if (info != null) {
                HotelRoomListResponse.PriceDetail detail = new HotelRoomListResponse.PriceDetail();
                detail.setDate(info.getCheckInDate());
                detail.setPrice(product.getPrice());

                // 添加商品ID和属性值ID到价格明细
                detail.setProductId(product.getId());

                // 查找对应的商品属性值ID
                Integer attrValueId = findProductAttrValueId(product.getId());
                detail.setAttrValueId(attrValueId);

                // 根据日期判断价格类型（使用中国日历服务）
                DateTypeEnum dateType = determineDateType(info.getCheckInDate());
                detail.setPriceType(dateType.name());
                detail.setPriceTypeName(dateType.getDesc());

                priceDetails.add(detail);
            }
        }
        priceInfo.setPriceDetails(priceDetails);

        return priceInfo;
    }

    /**
     * 计算可用性信息
     */
    private HotelRoomListResponse.AvailabilityInfo calculateAvailability(List<Product> roomProducts, List<String> dateRange) {
        HotelRoomListResponse.AvailabilityInfo availability = new HotelRoomListResponse.AvailabilityInfo();

        // 检查是否所有日期都有商品且有库存
        boolean allAvailable = roomProducts.size() >= dateRange.size() &&
                roomProducts.stream().allMatch(product -> product.getStock() > 0);

        availability.setAvailable(allAvailable);

        if (allAvailable) {
            // 取最小库存作为剩余房间数
            Integer minStock = roomProducts.stream()
                    .mapToInt(Product::getStock)
                    .min()
                    .orElse(0);
            availability.setRemainingRooms(minStock);
        } else {
            availability.setRemainingRooms(0);
            availability.setUnavailableReason("选择日期内房间已售罄或暂不可预订");
        }

        return availability;
    }

    /**
     * 根据房型名称查找房型信息
     */
    private HotelRoom findHotelRoomByName(Integer merId, String roomTypeName) {
        try {
            LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(HotelRoom::getMerId, merId)
                   .eq(HotelRoom::getRoomName, roomTypeName)
                   .eq(HotelRoom::getStatus, 1) // 启用状态
                   .eq(HotelRoom::getIsDel, 0); // 未删除

            List<HotelRoom> rooms = hotelRoomService.list(wrapper);
            return CollUtil.isNotEmpty(rooms) ? rooms.get(0) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 使用正则表达式解析商品名称（备用方法）
     */
    private HotelProductInfo parseProductNameWithRegex(String productName) {
        try {
            // 使用正则表达式提取日期 yyyy-MM-dd
            String datePattern = "\\d{4}-\\d{2}-\\d{2}";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(datePattern);
            java.util.regex.Matcher matcher = pattern.matcher(productName);

            if (matcher.find()) {
                String dateStr = matcher.group();

                // 移除日期部分，剩余部分按分隔符分割
                String remainingPart = productName.replace(dateStr, "").trim();
                // 移除可能的尾部分隔符
                if (remainingPart.endsWith("-")) {
                    remainingPart = remainingPart.substring(0, remainingPart.length() - 1);
                }

                String[] parts = remainingPart.split("-");

                if (parts.length >= 2) {
                    HotelProductInfo result = HotelProductInfo.builder()
                            .hotelName(parts[0])
                            .roomTypeName(parts[1])
                            .checkInDate(dateStr)
                            .build();

                    return result;
                }
            }
        } catch (Exception e) {
            // 解析失败，返回null
        }

        return null;
    }

    /**
     * 缓存商户信息查询（性能优化：减少重复查询）
     * 缓存时间：30分钟，商户信息变更不频繁
     */
    @Cacheable(value = "hotel:merchant", key = "#merId", unless = "#result == null")
    private Merchant getCachedMerchant(Integer merId) {
        try {
            return merchantService.getByIdException(merId);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 批量查询房型信息（性能优化：避免N+1查询 + Redis缓存）
     * 缓存时间：30分钟，房型信息变更不频繁但需要及时更新
     */
    @Cacheable(value = "hotel:room:details",
            key = "#merId + ':' + #roomTypeNames.hashCode()",
            unless = "#result.isEmpty()")
    private Map<String, HotelRoom> batchQueryHotelRooms(Integer merId, Set<String> roomTypeNames) {
        if (CollUtil.isEmpty(roomTypeNames)) {
            return new HashMap<>();
        }

        // 批量查询所有房型信息
        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getMerId, merId)
               .in(HotelRoom::getRoomName, roomTypeNames)
               .eq(HotelRoom::getStatus, 1)
               .eq(HotelRoom::getIsDel, 0);

        List<HotelRoom> hotelRooms = hotelRoomService.list(wrapper);

        // 转换为Map便于查找
        return hotelRooms.stream()
                .collect(Collectors.toMap(HotelRoom::getRoomName, room -> room, (existing, replacement) -> existing));
    }

    /**
     * 智能查询策略：根据日期数量选择最优查询方式
     */
    private List<Product> optimizedQueryProductsByDates(Integer hotelId, List<String> dateRange) {
        if (CollUtil.isEmpty(dateRange)) {
            return new ArrayList<>();
        }

        if (dateRange.size() <= 2) {
            return queryProductsByDatesLoop(hotelId, dateRange);
        } else {
            return queryProductsByDatesOr(hotelId, dateRange);
        }
    }

    /**
     * 循环查询方式（适合少量日期，索引友好）
     */
    private List<Product> queryProductsByDatesLoop(Integer hotelId, List<String> dateRange) {
        List<Product> allProducts = new ArrayList<>();

        for (String date : dateRange) {
            LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Product::getMerId, hotelId)
                   .eq(Product::getType, HotelConstants.HOTEL_PRODUCT_TYPE)
                   .like(Product::getName, date)
                   .eq(Product::getIsDel, false)
                   .eq(Product::getIsShow, true);

            List<Product> dateProducts = productService.list(wrapper);
            allProducts.addAll(dateProducts);
        }

        return allProducts;
    }

    /**
     * OR查询方式（适合大量日期，减少查询次数）
     */
    private List<Product> queryProductsByDatesOr(Integer hotelId, List<String> dateRange) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getMerId, hotelId)
               .eq(Product::getType, HotelConstants.HOTEL_PRODUCT_TYPE)
               .eq(Product::getIsDel, false)
               .eq(Product::getIsShow, true);

        // 使用OR条件匹配所有日期
        wrapper.and(w -> {
            for (int i = 0; i < dateRange.size(); i++) {
                if (i == 0) {
                    w.like(Product::getName, dateRange.get(i));
                } else {
                    w.or().like(Product::getName, dateRange.get(i));
                }
            }
        });

        List<Product> allProducts = productService.list(wrapper);
        return allProducts;
    }

    /**
     * 根据日期判断日期类型（使用中国日历服务）
     */
    private DateTypeEnum determineDateType(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr);
            DateTypeEnum dateType = chineseCalendarService.getDateType(date);
            return dateType;
        } catch (Exception e) {
            return DateTypeEnum.WORKDAY;
        }
    }

    /**
     * 清除所有酒店相关缓存（用于解决序列化问题后的缓存清理）
     */
    @Caching(evict = {
        @CacheEvict(value = "hotel:merchant", allEntries = true),
        @CacheEvict(value = "hotel:room:details", allEntries = true),
        @CacheEvict(value = "hotel:room:list", allEntries = true),
        @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public void clearAllHotelCache() {
        // 清除所有酒店相关缓存
    }


    /**
     * 查找商品的属性值ID
     *
     * @param productId 商品ID
     * @return 属性值ID，如果未找到返回null
     */
    private Integer findProductAttrValueId(Integer productId) {
        try {
            LambdaQueryWrapper<ProductAttrValue> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductAttrValue::getProductId, productId)
                    .eq(ProductAttrValue::getType, HotelConstants.HOTEL_PRODUCT_TYPE) // 酒店商品类型：7
                    .eq(ProductAttrValue::getMarketingType, 0) // 基础商品，非营销商品
                   .eq(ProductAttrValue::getIsDel, false)
                   .eq(ProductAttrValue::getIsShow, true)
                   .last("LIMIT 1");

            ProductAttrValue attrValue = productAttrValueService.getOne(wrapper);
            if (attrValue != null) {
                return attrValue.getId();
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
}
