package com.zbkj.service.service.hotel.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.admin.SystemAdmin;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.model.hotel.HotelRoomPriceStrategy;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategyCreateRequest;
import com.zbkj.common.request.hotel.HotelRoomRequest;
import com.zbkj.common.request.hotel.HotelRoomSearchRequest;
import com.zbkj.common.request.hotel.HotelRoomWithPricesRequest;
import com.zbkj.common.response.hotel.HotelRoomResponse;
import com.zbkj.common.utils.SecurityUtil;
import com.zbkj.service.dao.hotel.HotelRoomDao;
import com.zbkj.service.service.SystemAdminService;
import com.zbkj.service.service.hotel.HotelProductSyncService;
import com.zbkj.service.service.hotel.HotelRoomPriceStrategyService;
import com.zbkj.service.service.hotel.HotelRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酒店房间服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelRoomServiceImpl extends ServiceImpl<HotelRoomDao, HotelRoom> implements HotelRoomService {

    @Autowired
    private SystemAdminService systemAdminService;

    @Autowired
    private HotelRoomPriceStrategyService priceStrategyService;

    @Autowired
    private HotelProductSyncService productSyncService;

    @Override
    public List<HotelRoom> getAllActiveRooms() {
        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getStatus, 1) // 启用状态
                .eq(HotelRoom::getIsDel, 0)   // 未删除
               .orderByAsc(HotelRoom::getSort) // 按排序字段排序
               .orderByDesc(HotelRoom::getCreateTime); // 创建时间倒序

        return list(wrapper);
    }

    @Override
    public List<HotelRoom> getActiveRoomsByMerchant(Integer merchantId) {
        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getMerId, merchantId)
               .eq(HotelRoom::getStatus, 1)
                .eq(HotelRoom::getIsDel, 0)   // 未删除条件
               .orderByAsc(HotelRoom::getSort)
               .orderByDesc(HotelRoom::getCreateTime);

        return list(wrapper);
    }

    @Override
    public HotelRoom getById(Integer roomId) {
        if (roomId == null) {
            return null;
        }

        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getId, roomId);

        return getOne(wrapper);
    }

    @Override
    public boolean isActiveRoom(Integer roomId) {
        if (roomId == null) {
            return false;
        }

        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getId, roomId)
                .eq(HotelRoom::getStatus, 1)
                .eq(HotelRoom::getIsDel, 0);   // 未删除条件

        return count(wrapper) > 0;
    }

    @Override
    public boolean updateRoomStatus(Integer roomId, Integer status) {
        if (roomId == null || status == null) {
            return false;
        }

        HotelRoom room = new HotelRoom();
        room.setId(roomId);
        room.setStatus(status);

        return updateById(room);
    }

    @Override
    public boolean updateRoomStock(Integer roomId, Integer totalRooms) {
        if (roomId == null || totalRooms == null || totalRooms < 0) {
            return false;
        }

        HotelRoom room = new HotelRoom();
        room.setId(roomId);
        room.setTotalRooms(totalRooms);

        return updateById(room);
    }

    @Override
    public PageInfo<HotelRoomResponse> getMerchantPage(HotelRoomSearchRequest request, PageParamRequest pageParamRequest) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        Page<HotelRoom> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getMerId, merchantId)
                .eq(HotelRoom::getIsDel, 0);   // 未删除条件

        // 搜索条件
        if (StrUtil.isNotBlank(request.getRoomName())) {
            wrapper.like(HotelRoom::getRoomName, request.getRoomName());
        }
        if (StrUtil.isNotBlank(request.getRoomType())) {
            wrapper.eq(HotelRoom::getRoomType, request.getRoomType());
        }
        if (request.getStatus() != null) {
            wrapper.eq(HotelRoom::getStatus, request.getStatus());
        }
        if (StrUtil.isNotBlank(request.getBedType())) {
            wrapper.eq(HotelRoom::getBedType, request.getBedType());
        }
        if (request.getMaxGuests() != null) {
            wrapper.eq(HotelRoom::getMaxGuests, request.getMaxGuests());
        }

        // 排序
        wrapper.orderByAsc(HotelRoom::getSort)
               .orderByDesc(HotelRoom::getCreateTime);

        List<HotelRoom> list = list(wrapper);

        // 转换为响应对象
        List<HotelRoomResponse> responseList = list.stream().map(this::convertToResponse).collect(Collectors.toList());

        return CommonPage.copyPageInfo(page, responseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "hotel:room:details", allEntries = true),
        @CacheEvict(value = "hotel:room:list", allEntries = true),
        @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean saveHotelRoom(HotelRoomRequest request) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查房型名称是否重复
        if (checkRoomNameExists(request.getRoomName(), merchantId, null)) {
            throw new CrmebException("房型名称已存在");
        }

        HotelRoom hotelRoom = new HotelRoom();
        BeanUtil.copyProperties(request, hotelRoom);
        hotelRoom.setMerId(merchantId);
        hotelRoom.setCreateId(admin.getId());
        hotelRoom.setCreateTime(new Date());
        hotelRoom.setUpdateTime(new Date());

        // 设置默认值
        if (hotelRoom.getSort() == null) {
            hotelRoom.setSort(0);
        }
        if (hotelRoom.getStatus() == null) {
            hotelRoom.setStatus(1);
        }

        boolean result = super.save(hotelRoom);

        // 保存成功后，触发商品同步
        if (result) {
            try {
                productSyncService.syncHotelProductsByRoom(hotelRoom.getId());
            } catch (Exception e) {
                log.warn("房型保存后同步商品失败，房型ID: {}", hotelRoom.getId(), e);
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveHotelRoomWithPrices(HotelRoomWithPricesRequest request) {
        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查房型名称是否重复
        if (checkRoomNameExists(request.getRoomData().getRoomName(), merchantId, null)) {
            throw new CrmebException("房型名称已存在");
        }

        // 1. 创建房型
        HotelRoom hotelRoom = new HotelRoom();
        BeanUtil.copyProperties(request.getRoomData(), hotelRoom);
        hotelRoom.setMerId(merchantId);
        hotelRoom.setCreateId(admin.getId());
        hotelRoom.setCreateTime(new Date());
        hotelRoom.setUpdateTime(new Date());

        // 设置默认值
        if (hotelRoom.getSort() == null) {
            hotelRoom.setSort(0);
        }
        if (hotelRoom.getStatus() == null) {
            hotelRoom.setStatus(1);
        }

        boolean roomResult = super.save(hotelRoom);
        if (!roomResult) {
            throw new CrmebException("房型创建失败");
        }

        // 2. 创建价格策略
        for (HotelPriceStrategyCreateRequest priceRequest : request.getPriceStrategies()) {
            HotelRoomPriceStrategy priceStrategy = new HotelRoomPriceStrategy();
            BeanUtil.copyProperties(priceRequest, priceStrategy);
            priceStrategy.setMerId(merchantId);
            priceStrategy.setRoomId(hotelRoom.getId()); // 设置新创建的房型ID
            priceStrategy.setCreateId(admin.getId());
            priceStrategy.setCreateTime(new Date());
            priceStrategy.setUpdateTime(new Date());

            // 设置默认值
            if (priceStrategy.getStatus() == null) {
                priceStrategy.setStatus(1);
            }

            boolean priceResult = priceStrategyService.save(priceStrategy);
            if (!priceResult) {
                throw new CrmebException("价格策略创建失败");
            }
        }

        // 3. 触发商品同步
        try {
            productSyncService.syncHotelProductsByRoom(hotelRoom.getId());
        } catch (Exception e) {
            log.warn("房型创建后同步商品失败，房型ID: {}", hotelRoom.getId(), e);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "hotel:room:details", allEntries = true),
        @CacheEvict(value = "hotel:room:list", allEntries = true),
        @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean updateHotelRoom(HotelRoomRequest request) {
        if (request.getId() == null) {
            throw new CrmebException("房型ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        // 检查房型是否存在且属于当前商户
        HotelRoom existRoom = getById(request.getId());
        if (existRoom == null) {
            throw new CrmebException("房型不存在");
        }
        if (!existRoom.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限操作此房型");
        }

        // 检查房型名称是否重复
        if (checkRoomNameExists(request.getRoomName(), merchantId, request.getId())) {
            throw new CrmebException("房型名称已存在");
        }

        HotelRoom hotelRoom = new HotelRoom();
        BeanUtil.copyProperties(request, hotelRoom);
        hotelRoom.setMerId(merchantId);
        hotelRoom.setUpdateTime(new Date());

        boolean result = updateById(hotelRoom);

        // 更新成功后，触发商品同步
        if (result) {
            try {
                productSyncService.syncHotelProductsByRoom(hotelRoom.getId());
            } catch (Exception e) {
                log.warn("房型更新后同步商品失败，房型ID: {}", hotelRoom.getId(), e);
            }
        }

        return result;
    }

    @Override
    public HotelRoomResponse getInfo(Integer id) {
        if (id == null) {
            throw new CrmebException("房型ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelRoom hotelRoom = getById(id);
        if (hotelRoom == null) {
            throw new CrmebException("房型不存在");
        }
        if (!hotelRoom.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限查看此房型");
        }

        return convertToResponse(hotelRoom);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
        @CacheEvict(value = "hotel:room:details", allEntries = true),
        @CacheEvict(value = "hotel:room:list", allEntries = true),
        @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public boolean delete(Integer id) {
        if (id == null) {
            throw new CrmebException("房型ID不能为空");
        }

        // 获取当前商户ID
        SystemAdmin admin = SecurityUtil.getLoginUserVo().getUser();
        Integer merchantId = admin.getMerId();

        HotelRoom hotelRoom = getById(id);
        if (hotelRoom == null) {
            throw new CrmebException("房型不存在");
        }
        if (!hotelRoom.getMerId().equals(merchantId)) {
            throw new CrmebException("无权限删除此房型");
        }

        // 检查是否有关联的价格策略
        long priceStrategyCount = priceStrategyService.countByRoomId(id);
        if (priceStrategyCount > 0) {
            throw new CrmebException("该房型存在价格策略，无法删除");
        }

        // 使用MyBatis-Plus逻辑删除功能
        return removeById(id);
    }

    @Override
    public boolean checkRoomNameExists(String roomName, Integer merchantId, Integer excludeId) {
        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getRoomName, roomName)
                .eq(HotelRoom::getMerId, merchantId)
                .eq(HotelRoom::getIsDel, 0);   // 未删除条件

        if (excludeId != null) {
            wrapper.ne(HotelRoom::getId, excludeId);
        }

        return count(wrapper) > 0;
    }

    /**
     * 转换为响应对象
     */
    private HotelRoomResponse convertToResponse(HotelRoom hotelRoom) {
        HotelRoomResponse response = new HotelRoomResponse();
        BeanUtil.copyProperties(hotelRoom, response);

        // 设置状态描述
        response.setStatusDesc(hotelRoom.getStatus() == 1 ? "启用" : "禁用");

        // 获取商户名称
        try {
            SystemAdmin merchant = systemAdminService.getById(hotelRoom.getMerId());
            if (merchant != null) {
                response.setMerchantName(merchant.getRealName());
            }
        } catch (Exception e) {
            log.warn("获取商户名称失败，商户ID: {}", hotelRoom.getMerId(), e);
        }

        // 获取价格策略数量
        try {
            long priceStrategyCount = priceStrategyService.countByRoomId(hotelRoom.getId());
            response.setPriceStrategyCount((int) priceStrategyCount);
        } catch (Exception e) {
            log.warn("获取价格策略数量失败，房型ID: {}", hotelRoom.getId(), e);
            response.setPriceStrategyCount(0);
        }

        // TODO: 获取已生成商品数量（需要ProductService支持）
        response.setProductCount(0);

        return response;
    }
}
