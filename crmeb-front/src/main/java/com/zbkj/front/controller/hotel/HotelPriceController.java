package com.zbkj.front.controller.hotel;

import com.zbkj.common.request.hotel.HotelBatchPriceRequest;
import com.zbkj.common.request.hotel.HotelPriceCalendarRequest;
import com.zbkj.common.response.hotel.HotelPriceCalendarResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.hotel.HotelRoomPriceStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 酒店价格查询控制器 - 用户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/front/hotel/price")
@Api(tags = "用户端 - 酒店价格查询")
@Validated
public class HotelPriceController {

    @Autowired
    private HotelRoomPriceStrategyService priceStrategyService;

    /**
     * 获取房型价格日历
     * 用于前端日历显示房型在指定月份的每日价格
     */
    @ApiOperation(value = "获取房型价格日历")
    @RequestMapping(value = "/calendar", method = RequestMethod.GET)
    public CommonResult<HotelPriceCalendarResponse> getPriceCalendar(@Validated HotelPriceCalendarRequest request) {
        try {
            log.info("前端查询房型价格日历 - 房型ID: {}, 年月: {}-{}",
                    request.getRoomId(), request.getYear(), request.getMonth());

            HotelPriceCalendarResponse response = priceStrategyService.getFrontPriceCalendar(request);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("获取房型价格日历失败", e);
            return CommonResult.failed("获取价格信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取房型指定日期的价格
     * 用于前端快速查询单个日期的价格
     */
    @ApiOperation(value = "获取房型指定日期价格")
    @RequestMapping(value = "/day", method = RequestMethod.GET)
    public CommonResult<HotelPriceCalendarResponse.DayPrice> getDayPrice(
            @RequestParam @NotNull(message = "房型ID不能为空") Integer roomId,
            @RequestParam @NotNull(message = "日期不能为空") String date) {
        try {
            log.info("前端查询房型单日价格 - 房型ID: {}, 日期: {}", roomId, date);

            HotelPriceCalendarResponse.DayPrice dayPrice = priceStrategyService.getFrontDayPrice(roomId, date);
            return CommonResult.success(dayPrice);
        } catch (Exception e) {
            log.error("获取房型单日价格失败", e);
            return CommonResult.failed("获取价格信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取房型多个日期的价格
     * 用于前端批量查询价格，减少请求次数
     */
    @ApiOperation(value = "批量获取房型多日期价格")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    public CommonResult<java.util.List<HotelPriceCalendarResponse.DayPrice>> getBatchDayPrices(
            @RequestBody @Validated HotelBatchPriceRequest request) {
        try {
            log.info("前端批量查询房型价格 - 房型ID: {}, 日期数量: {}",
                    request.getRoomId(), request.getDates().size());

            java.util.List<HotelPriceCalendarResponse.DayPrice> dayPrices =
                    priceStrategyService.getFrontBatchDayPrices(request.getRoomId(), request.getDates());
            return CommonResult.success(dayPrices);
        } catch (Exception e) {
            log.error("批量获取房型价格失败", e);
            return CommonResult.failed("获取价格信息失败: " + e.getMessage());
        }
    }
}
